//@version=6
indicator("高低点连线指标", shorttitle="高低点连线", overlay=true, max_lines_count=500, max_labels_count=500)

// ===========================
// 参数设置
// ===========================

var g1 = "=== 高低点识别设置 ==="
pivot_length = input.int(5, title="枢轴长度", minval=1, maxval=50, group=g1, tooltip="用于识别高低点的左右K线数量")
min_bars_between = input.int(10, title="高低点最小间隔", minval=1, maxval=100, group=g1, tooltip="两个高低点之间的最小K线数量")

var g2 = "=== 显示设置 ==="
show_high_lines = input.bool(true, title="显示高点连线", group=g2)
show_low_lines = input.bool(true, title="显示低点连线", group=g2)
show_labels = input.bool(true, title="显示高低点标签", group=g2)
line_extend = input.string("none", title="连线延伸", options=["none", "left", "right", "both"], group=g2)

var g3 = "=== 颜色设置 ==="
high_line_color = input.color(color.red, title="高点连线颜色", group=g3)
low_line_color = input.color(color.green, title="低点连线颜色", group=g3)
high_label_color = input.color(color.red, title="高点标签颜色", group=g3)
low_label_color = input.color(color.green, title="低点标签颜色", group=g3)
line_width = input.int(2, title="连线宽度", minval=1, maxval=5, group=g3)

var g4 = "=== 高级设置 ==="
max_lines_display = input.int(3, title="最大显示连线数", minval=1, maxval=10, group=g4, tooltip="屏幕上同时显示的最大连线数量")
enable_alerts = input.bool(true, title="启用警报", group=g4)
extend_to_current = input.bool(true, title="延伸到当前K线", group=g4, tooltip="将最新连线延伸到当前K线位置")
show_realtime_points = input.bool(true, title="显示实时高低点", group=g4, tooltip="显示当前可能的高低点（未确认）")

// ===========================
// 高低点识别函数
// ===========================

// 识别枢轴高点
pivot_high = ta.pivothigh(high, pivot_length, pivot_length)
// 识别枢轴低点
pivot_low = ta.pivotlow(low, pivot_length, pivot_length)

// ===========================
// 存储高低点数据
// ===========================

// 高点数据结构
type HighPoint
    int bar_index
    float price
    int time

// 低点数据结构
type LowPoint
    int bar_index
    float price
    int time

// 存储最近的高低点
var array<HighPoint> recent_highs = array.new<HighPoint>()
var array<LowPoint> recent_lows = array.new<LowPoint>()

// 存储连线对象
var array<line> high_lines = array.new<line>()
var array<line> low_lines = array.new<line>()
var array<label> high_labels = array.new<label>()
var array<label> low_labels = array.new<label>()

// ===========================
// 高低点检测和存储
// ===========================

// 检测到新的高点
if not na(pivot_high)
    // 检查是否满足最小间隔要求
    can_add_high = true
    if array.size(recent_highs) > 0
        last_high = array.get(recent_highs, array.size(recent_highs) - 1)
        if bar_index - last_high.bar_index < min_bars_between
            can_add_high := false

    if can_add_high
        // 创建新的高点
        new_high = HighPoint.new(bar_index[pivot_length], pivot_high, time[pivot_length])
        array.push(recent_highs, new_high)

        // 限制数组大小，保留最近的高点
        if array.size(recent_highs) > 4
            array.shift(recent_highs)

        // 添加高点标签（修正位置到实际高点）
        if show_labels
            label_text = "H: " + str.tostring(pivot_high, "#.##")
            new_label = label.new(new_high.bar_index, pivot_high, text=label_text, style=label.style_label_down, color=high_label_color, textcolor=color.white, size=size.small)
            array.push(high_labels, new_label)

            // 限制标签数量
            if array.size(high_labels) > 3
                old_label = array.shift(high_labels)
                label.delete(old_label)

// 检测到新的低点
if not na(pivot_low)
    // 检查是否满足最小间隔要求
    can_add_low = true
    if array.size(recent_lows) > 0
        last_low = array.get(recent_lows, array.size(recent_lows) - 1)
        if bar_index - last_low.bar_index < min_bars_between
            can_add_low := false

    if can_add_low
        // 创建新的低点
        new_low = LowPoint.new(bar_index[pivot_length], pivot_low, time[pivot_length])
        array.push(recent_lows, new_low)

        // 限制数组大小，保留最近的低点
        if array.size(recent_lows) > 4
            array.shift(recent_lows)

        // 添加低点标签（修正位置到实际低点）
        if show_labels
            label_text = "L: " + str.tostring(pivot_low, "#.##")
            new_label = label.new(new_low.bar_index, pivot_low, text=label_text, style=label.style_label_up, color=low_label_color, textcolor=color.white, size=size.small)
            array.push(low_labels, new_label)

            // 限制标签数量
            if array.size(low_labels) > 3
                old_label = array.shift(low_labels)
                label.delete(old_label)

// ===========================
// 连线绘制函数
// ===========================

// 绘制高点连线
draw_high_lines() =>
    if show_high_lines and array.size(recent_highs) >= 2
        // 清除所有旧的高点连线
        if array.size(high_lines) > 0
            for i = 0 to array.size(high_lines) - 1
                line.delete(array.get(high_lines, i))
            array.clear(high_lines)

        // 只绘制最近3条高点连线
        total_points = array.size(recent_highs)
        if total_points >= 2
            lines_to_draw = math.min(3, total_points - 1)
            start_index = math.max(0, total_points - lines_to_draw - 1)
            for i = start_index to total_points - 2
                if i >= 0 and i + 1 < total_points
                    point1 = array.get(recent_highs, i)
                    point2 = array.get(recent_highs, i + 1)

                    // 创建连线，最新的连线使用虚线样式
                    is_latest_line = (i == total_points - 2)
                    line_style = is_latest_line ? line.style_dashed : line.style_solid
                    line_width_used = is_latest_line ? line_width + 1 : line_width

                    // 确定连线延伸方式
                    extend_type = switch line_extend
                        "left" => extend.left
                        "right" => extend.right
                        "both" => extend.both
                        => extend.none

                    // 最新连线自动向右延伸
                    if is_latest_line and extend_to_current
                        extend_type := extend.right

                    // 创建连线，使用内置延伸功能
                    new_line = line.new(x1=point1.bar_index, y1=point1.price, x2=point2.bar_index, y2=point2.price, color=high_line_color, width=line_width_used, style=line_style, extend=extend_type)
                    array.push(high_lines, new_line)

// 绘制低点连线
draw_low_lines() =>
    if show_low_lines and array.size(recent_lows) >= 2
        // 清除所有旧的低点连线
        if array.size(low_lines) > 0
            for i = 0 to array.size(low_lines) - 1
                line.delete(array.get(low_lines, i))
            array.clear(low_lines)

        // 只绘制最近3条低点连线
        total_points = array.size(recent_lows)
        if total_points >= 2
            lines_to_draw = math.min(3, total_points - 1)
            start_index = math.max(0, total_points - lines_to_draw - 1)
            for i = start_index to total_points - 2
                if i >= 0 and i + 1 < total_points
                    point1 = array.get(recent_lows, i)
                    point2 = array.get(recent_lows, i + 1)

                    // 创建连线，最新的连线使用虚线样式
                    is_latest_line = (i == total_points - 2)
                    line_style = is_latest_line ? line.style_dashed : line.style_solid
                    line_width_used = is_latest_line ? line_width + 1 : line_width

                    // 确定连线延伸方式
                    extend_type = switch line_extend
                        "left" => extend.left
                        "right" => extend.right
                        "both" => extend.both
                        => extend.none

                    // 最新连线自动向右延伸
                    if is_latest_line and extend_to_current
                        extend_type := extend.right

                    // 创建连线，使用内置延伸功能
                    new_line = line.new(x1=point1.bar_index, y1=point1.price, x2=point2.bar_index, y2=point2.price, color=low_line_color, width=line_width_used, style=line_style, extend=extend_type)
                    array.push(low_lines, new_line)

// ===========================
// 特殊功能：最近两次高点连线 (已集成到主绘制函数中)
// ===========================

// 注释掉独立的特殊连线函数，避免重复绘制
// draw_latest_high_line() =>
//     if array.size(recent_highs) >= 2
//         latest_high = array.get(recent_highs, array.size(recent_highs) - 1)
//         second_latest_high = array.get(recent_highs, array.size(recent_highs) - 2)
//         line.new(x1=second_latest_high.bar_index, y1=second_latest_high.price, x2=latest_high.bar_index, y2=latest_high.price, color=color.new(high_line_color, 0), width=line_width + 1, style=line.style_dashed, extend=extend.right)

// draw_latest_low_line() =>
//     if array.size(recent_lows) >= 2
//         latest_low = array.get(recent_lows, array.size(recent_lows) - 1)
//         second_latest_low = array.get(recent_lows, array.size(recent_lows) - 2)
//         line.new(x1=second_latest_low.bar_index, y1=second_latest_low.price, x2=latest_low.bar_index, y2=latest_low.price, color=color.new(low_line_color, 0), width=line_width + 1, style=line.style_dashed, extend=extend.right)

// ===========================
// 执行绘制
// ===========================

// 每个新K线都重新绘制连线
if barstate.islast
    draw_high_lines()
    draw_low_lines()

// 注释掉额外的连线绘制，避免重复
// if not na(pivot_high)
//     if array.size(recent_highs) >= 2
//         draw_latest_high_line()

// if not na(pivot_low)
//     if array.size(recent_lows) >= 2
//         draw_latest_low_line()

// ===========================
// 趋势分析功能
// ===========================

// 分析高点趋势
analyze_high_trend() =>
    if array.size(recent_highs) >= 2
        latest_high = array.get(recent_highs, array.size(recent_highs) - 1)
        second_latest_high = array.get(recent_highs, array.size(recent_highs) - 2)

        if latest_high.price > second_latest_high.price
            "上升高点"
        else if latest_high.price < second_latest_high.price
            "下降高点"
        else
            "平行高点"
    else
        "数据不足"

// 分析低点趋势
analyze_low_trend() =>
    if array.size(recent_lows) >= 2
        latest_low = array.get(recent_lows, array.size(recent_lows) - 1)
        second_latest_low = array.get(recent_lows, array.size(recent_lows) - 2)

        if latest_low.price > second_latest_low.price
            "上升低点"
        else if latest_low.price < second_latest_low.price
            "下降低点"
        else
            "平行低点"
    else
        "数据不足"

// ===========================
// 信息面板
// ===========================

var table info_table = table.new(position.top_right, 2, 8, bgcolor=color.new(color.white, 20), border_width=2)

if barstate.islast
    // 清除表格
    table.clear(info_table, 0, 0, 1, 7)

    // 标题
    table.cell(info_table, 0, 0, "高低点连线分析", text_color=color.black, bgcolor=color.new(color.gray, 50), text_size=size.normal)
    table.cell(info_table, 1, 0, "", text_color=color.black, bgcolor=color.new(color.gray, 50))

    // 高点信息
    table.cell(info_table, 0, 1, "高点数量", text_color=color.black, text_size=size.small)
    table.cell(info_table, 1, 1, str.tostring(array.size(recent_highs)), text_color=color.black, text_size=size.small)

    table.cell(info_table, 0, 2, "高点趋势", text_color=color.black, text_size=size.small)
    high_trend = analyze_high_trend()
    high_trend_color = high_trend == "上升高点" ? color.green : high_trend == "下降高点" ? color.red : color.gray
    table.cell(info_table, 1, 2, high_trend, text_color=high_trend_color, text_size=size.small)

    // 低点信息
    table.cell(info_table, 0, 3, "低点数量", text_color=color.black, text_size=size.small)
    table.cell(info_table, 1, 3, str.tostring(array.size(recent_lows)), text_color=color.black, text_size=size.small)

    table.cell(info_table, 0, 4, "低点趋势", text_color=color.black, text_size=size.small)
    low_trend = analyze_low_trend()
    low_trend_color = low_trend == "上升低点" ? color.green : low_trend == "下降低点" ? color.red : color.gray
    table.cell(info_table, 1, 4, low_trend, text_color=low_trend_color, text_size=size.small)

    // 最新高低点价格
    if array.size(recent_highs) > 0
        latest_high = array.get(recent_highs, array.size(recent_highs) - 1)
        table.cell(info_table, 0, 5, "最新高点", text_color=color.black, text_size=size.small)
        table.cell(info_table, 1, 5, str.tostring(latest_high.price, "#.##"), text_color=color.red, text_size=size.small)
    else
        table.cell(info_table, 0, 5, "最新高点", text_color=color.black, text_size=size.small)
        table.cell(info_table, 1, 5, "无数据", text_color=color.gray, text_size=size.small)

    if array.size(recent_lows) > 0
        latest_low = array.get(recent_lows, array.size(recent_lows) - 1)
        table.cell(info_table, 0, 6, "最新低点", text_color=color.black, text_size=size.small)
        table.cell(info_table, 1, 6, str.tostring(latest_low.price, "#.##"), text_color=color.green, text_size=size.small)
    else
        table.cell(info_table, 0, 6, "最新低点", text_color=color.black, text_size=size.small)
        table.cell(info_table, 1, 6, "无数据", text_color=color.gray, text_size=size.small)

    // 连线统计
    table.cell(info_table, 0, 7, "连线数量", text_color=color.black, text_size=size.small)
    total_lines = array.size(high_lines) + array.size(low_lines)
    table.cell(info_table, 1, 7, str.tostring(total_lines), text_color=color.blue, text_size=size.small)

// ===========================
// 警报功能
// ===========================

// 新高点警报
if enable_alerts and not na(pivot_high)
    alert("检测到新的高点: " + str.tostring(pivot_high, "#.##"), alert.freq_once_per_bar)

// 新低点警报
if enable_alerts and not na(pivot_low)
    alert("检测到新的低点: " + str.tostring(pivot_low, "#.##"), alert.freq_once_per_bar)

// 趋势变化警报
var string last_high_trend = ""
var string last_low_trend = ""

current_high_trend = analyze_high_trend()
current_low_trend = analyze_low_trend()

if enable_alerts and current_high_trend != last_high_trend and current_high_trend != "数据不足"
    alert("高点趋势变化: " + current_high_trend, alert.freq_once_per_bar)
    last_high_trend := current_high_trend

if enable_alerts and current_low_trend != last_low_trend and current_low_trend != "数据不足"
    alert("低点趋势变化: " + current_low_trend, alert.freq_once_per_bar)
    last_low_trend := current_low_trend

// ===========================
// 绘制当前价格相对位置
// ===========================

// 在最新高低点之间绘制当前价格位置
if array.size(recent_highs) > 0 and array.size(recent_lows) > 0 and barstate.islast
    latest_high = array.get(recent_highs, array.size(recent_highs) - 1)
    latest_low = array.get(recent_lows, array.size(recent_lows) - 1)

    // 计算当前价格在高低点之间的位置百分比
    if latest_high.price != latest_low.price and latest_high.price > latest_low.price
        price_position = (close - latest_low.price) / (latest_high.price - latest_low.price) * 100

        // 在图表上显示价格位置信息
        position_text = "价格位置: " + str.tostring(price_position, "#.#") + "%"
        position_color = price_position > 50 ? color.green : color.red

        // 删除之前的价格位置标签
        var label price_position_label = na
        if not na(price_position_label)
            label.delete(price_position_label)

        price_position_label := label.new(bar_index, close, text=position_text, style=label.style_label_left, color=position_color, textcolor=color.white, size=size.small)

// ===========================
// 支撑阻力分析
// ===========================

// 识别关键支撑阻力位
get_support_resistance() =>
    var float[] support_levels = array.new<float>()
    var float[] resistance_levels = array.new<float>()

    // 清除旧数据
    array.clear(support_levels)
    array.clear(resistance_levels)

    // 收集所有低点作为潜在支撑位
    if array.size(recent_lows) > 0
        for i = 0 to array.size(recent_lows) - 1
            low_point = array.get(recent_lows, i)
            array.push(support_levels, low_point.price)

    // 收集所有高点作为潜在阻力位
    if array.size(recent_highs) > 0
        for i = 0 to array.size(recent_highs) - 1
            high_point = array.get(recent_highs, i)
            array.push(resistance_levels, high_point.price)

    [support_levels, resistance_levels]

// 执行支撑阻力分析
[support_levels, resistance_levels] = get_support_resistance()

// ===========================
// 警报条件设置
// ===========================

alertcondition(not na(pivot_high), title="新高点检测", message="检测到新的枢轴高点")
alertcondition(not na(pivot_low), title="新低点检测", message="检测到新的枢轴低点")
alertcondition(array.size(recent_highs) >= 2 and not na(pivot_high), title="高点连线更新", message="高点连线已更新")
alertcondition(array.size(recent_lows) >= 2 and not na(pivot_low), title="低点连线更新", message="低点连线已更新")

// ===========================
// 实时高低点检测和显示
// ===========================

// 检测当前可能的高低点（未确认）
if show_realtime_points and barstate.islast
    // 检查当前是否可能是高点
    is_potential_high = true
    is_potential_low = true

    // 检查左侧K线
    for i = 1 to pivot_length
        if i <= bar_index and high[i] >= high
            is_potential_high := false
        if i <= bar_index and low[i] <= low
            is_potential_low := false

    // 显示潜在的高低点
    if is_potential_high
        label.new(bar_index, high, text="H?", style=label.style_label_down, color=color.new(high_label_color, 50), textcolor=color.white, size=size.tiny)

    if is_potential_low
        label.new(bar_index, low, text="L?", style=label.style_label_up, color=color.new(low_label_color, 50), textcolor=color.white, size=size.tiny)

// ===========================
// 连线与K线对齐优化
// ===========================

// ===========================
// 简化的延伸显示
// ===========================

// 连线会自动使用Pine Script内置的extend功能延伸
// 这样可以避免缩放时的坐标变形问题