[gd_resource type="Theme" load_steps=2 format=3]

[ext_resource type="FontFile" path="res://godot_sea_battle/resources/font_normal.tres" id="1"]

[resource]
default_font = ExtResource("1")
Button/colors/font_color = Color(0.88, 0.88, 0.88, 1)
Button/colors/font_color_disabled = Color(0.9, 0.9, 0.9, 0.2)
Button/colors/font_color_hover = Color(0.94, 0.94, 0.94, 1)
Button/colors/font_color_pressed = Color(1, 1, 1, 1)
Button/constants/hseparation = 2
Button/styles/disabled = null
Button/styles/focus = null
Button/styles/hover = null
Button/styles/normal = null
Button/styles/pressed = null
Label/colors/font_color = Color(1, 1, 1, 1)
Label/colors/font_color_shadow = Color(0, 0, 0, 0)
Label/constants/line_spacing = 3
Label/constants/shadow_as_outline = 0
Label/constants/shadow_offset_x = 1
Label/constants/shadow_offset_y = 1
Label/styles/normal = null