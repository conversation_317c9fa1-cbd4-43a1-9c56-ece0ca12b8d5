[gd_resource type="FontFile" load_steps=2 format=3]

[ext_resource path="res://godot_sea_battle/resources/fonts/Inter-Regular.ttf" type="FontFile" id=1]

[resource]
fallbacks = []
face_index = 0
embolden = 0.0
transform = Transform2D(1, 0, 0, 1, 0, 0)
cache/0/16/0/ascent = 0.0
cache/0/16/0/descent = 0.0
cache/0/16/0/underline_position = 0.0
cache/0/16/0/underline_thickness = 0.0
cache/0/16/0/scale = 1.0
cache/0/16/0/kerning_overrides/16/0 = Vector2(0, 0)
cache/0/24/0/ascent = 0.0
cache/0/24/0/descent = 0.0
cache/0/24/0/underline_position = 0.0
cache/0/24/0/underline_thickness = 0.0
cache/0/24/0/scale = 1.0
cache/0/24/0/kerning_overrides/24/0 = Vector2(0, 0)
cache/0/24/0/kerning_overrides/16/0 = Vector2(0, 0)