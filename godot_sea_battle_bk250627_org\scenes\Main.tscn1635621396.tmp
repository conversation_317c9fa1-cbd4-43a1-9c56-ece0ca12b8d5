[gd_scene load_steps=4 format=3 uid="uid://dbdd1tknymsyq"]

[ext_resource type="Script" uid="uid://hlh5mt8ew5yl" path="res://scripts/ui_controller.gd" id="1_yvmjx"]
[ext_resource type="Script" uid="uid://bbonlr7cuvufp" path="res://scripts/board_display.gd" id="2_4k5q9"]
[ext_resource type="Script" uid="uid://eaibqd52kko4" path="res://scripts/game.gd" id="3_0bbpv"]

[node name="Main" type="Node2D"]

[node name="Game" type="Node" parent="."]
unique_name_in_owner = true
script = ExtResource("3_0bbpv")

[node name="UI" type="Control" parent="."]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_yvmjx")

[node name="PlayerBoard" type="Control" parent="UI"]
anchors_preset = 0
offset_left = 50.0
offset_top = 50.0
offset_right = 450.0
offset_bottom = 450.0
script = ExtResource("2_4k5q9")

[node name="AIBoard" type="Control" parent="UI"]
anchors_preset = 0
offset_left = 550.0
offset_top = 50.0
offset_right = 950.0
offset_bottom = 450.0
script = ExtResource("2_4k5q9")

[node name="Controls" type="VBoxContainer" parent="UI"]
layout_mode = 0
offset_left = 50.0
offset_top = 500.0
offset_right = 950.0
offset_bottom = 600.0

[node name="ShipButtons" type="HBoxContainer" parent="UI/Controls"]
layout_mode = 2
theme_override_constants/separation = 20

[node name="Ship2" type="Button" parent="UI/Controls/ShipButtons"]
custom_minimum_size = Vector2(100, 40)
layout_mode = 2
text = "2格船"
metadata/ship_size = 2

[node name="Ship3" type="Button" parent="UI/Controls/ShipButtons"]
custom_minimum_size = Vector2(100, 40)
layout_mode = 2
text = "3格船"
metadata/ship_size = 3

[node name="Ship4" type="Button" parent="UI/Controls/ShipButtons"]
custom_minimum_size = Vector2(100, 40)
layout_mode = 2
text = "4格船"
metadata/ship_size = 4

[node name="Ship5" type="Button" parent="UI/Controls/ShipButtons"]
custom_minimum_size = Vector2(100, 40)
layout_mode = 2
text = "5格船"
metadata/ship_size = 5

[node name="Spacer" type="Control" parent="UI/Controls/ShipButtons"]
custom_minimum_size = Vector2(20, 0)
layout_mode = 2

[node name="RotateButton" type="Button" parent="UI/Controls/ShipButtons"]
custom_minimum_size = Vector2(100, 40)
layout_mode = 2
text = "旋转"

[node name="AutoPlaceButton" type="Button" parent="UI/Controls/ShipButtons"]
custom_minimum_size = Vector2(100, 40)
layout_mode = 2
text = "自动放置"

[node name="StartButton" type="Button" parent="UI/Controls/ShipButtons"]
custom_minimum_size = Vector2(100, 40)
layout_mode = 2
text = "开始游戏"

[node name="StatusLabel" type="Label" parent="UI/Controls"]
layout_mode = 2
text = "放置船只阶段"
horizontal_alignment = 1

[node name="MessageLabel" type="Label" parent="UI/Controls"]
layout_mode = 2
horizontal_alignment = 1
