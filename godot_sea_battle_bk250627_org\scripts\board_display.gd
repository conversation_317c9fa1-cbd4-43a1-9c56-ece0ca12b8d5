extends Control
class_name BoardDisplay

# 信号定义
signal cell_clicked(cell: Vector2i)

# 常量定义
const BACKGROUND_COLOR := Color(0.9, 0.9, 0.9, 1.0)

# 变量
var is_player_board: bool = false

# 游戏引用
@onready var game:Game = $"../../Game"

# 棋盘引用
var board = null
var show_ships: bool = true

# 棋盘格子大小
const CELL_SIZE = 40
const BOARD_SIZE = 10

# 颜色定义
const GRID_COLOR = Color(0.5, 0.5, 0.5)
const SHIP_COLOR = Color(0.2, 0.2, 0.8)
const HIT_COLOR = Color(0.8, 0.2, 0.2)
const MISS_COLOR = Color(0.7, 0.7, 0.7)

# 初始化游戏引用
func init_game():
	print_debug(game)
	if game:
		print_verbose("Game reference set successfully")
		if is_player_board:
			board = game.get_player_board()
		else:
			board = game.get_ai_board()
		queue_redraw()
	else:
		push_warning("Game reference is null")
	
# 初始化
func _ready():
	print_debug("ready")
	print_verbose("BoardDisplay _ready() called")
	# 确保接收鼠标输入
	mouse_filter = MOUSE_FILTER_STOP
	# 设置正确的大小
	custom_minimum_size = Vector2(BOARD_SIZE * CELL_SIZE, BOARD_SIZE * CELL_SIZE)
	size = custom_minimum_size  # 强制设置实际尺寸
	print_verbose("BoardDisplay size set to: %s" % size)
	init_game()

# 更新显示# 更新显示 - 修改参数名避免冲突
func update_display(new_board, new_show_ships: bool):
	# 存储当前棋盘状态
	board = new_board
	show_ships = new_show_ships
	queue_redraw()

# 绘制棋盘
func _draw():
	print_debug("BoardDisplay _draw() called - size: ", size)  # 调试输出尺寸
	
	# 检查游戏和棋盘引用
	if not game:
		push_warning("Cannot draw - game reference is null")
		# 绘制红色半透明背景表示错误状态
		draw_rect(Rect2(0, 0, size.x, size.y), Color(1, 0, 0, 0.3), true)
		draw_string(ThemeDB.fallback_font, Vector2(20, 20), "Game reference not set!", 0)  # 0 = left align
		return
		
	if not board:
		push_warning("Cannot draw - board reference is null")
		# 绘制橙色半透明背景表示错误状态
		draw_rect(Rect2(0, 0, size.x, size.y), Color(1, 0.5, 0, 0.3), true)
		draw_string(ThemeDB.fallback_font, Vector2(20, 20), "Board reference not set!", 0)
		return
	
	# 绘制背景
	draw_rect(Rect2(0, 0, BOARD_SIZE * CELL_SIZE, BOARD_SIZE * CELL_SIZE), BACKGROUND_COLOR, true)
	
	# 绘制网格
	for i in range(BOARD_SIZE + 1):
		# 垂直线
		draw_line(
			Vector2(i * CELL_SIZE, 0),
			Vector2(i * CELL_SIZE, BOARD_SIZE * CELL_SIZE),
			GRID_COLOR,
			2.0
		)
		# 水平线
		draw_line(
			Vector2(0, i * CELL_SIZE),
			Vector2(BOARD_SIZE * CELL_SIZE, i * CELL_SIZE),
			GRID_COLOR,
			2.0
		)
	
	if not board:
		return
		
	# 绘制棋盘状态
	for x in range(BOARD_SIZE):
		for y in range(BOARD_SIZE):
			var cell_rect = Rect2(x * CELL_SIZE + 2, y * CELL_SIZE + 2, CELL_SIZE - 4, CELL_SIZE - 4)
			
			# 绘制船只
			if show_ships and board.has_ship_at(x, y):
				var ship_color = SHIP_COLOR.darkened(0.3)  # 加深颜色
				draw_rect(cell_rect, ship_color, true)  # 填充
				draw_rect(cell_rect, Color.BLACK, false, 2.0)  # 黑色边框
			
			# 绘制命中
			if board.get_cell_state(x, y) == Board.CellState.HIT:
				draw_rect(cell_rect, HIT_COLOR, true)
				draw_line(
					Vector2(x * CELL_SIZE + 4, y * CELL_SIZE + 4),
					Vector2((x + 1) * CELL_SIZE - 4, (y + 1) * CELL_SIZE - 4),
					Color.WHITE,
					2.0
				)
				draw_line(
					Vector2((x + 1) * CELL_SIZE - 4, y * CELL_SIZE + 4),
					Vector2(x * CELL_SIZE + 4, (y + 1) * CELL_SIZE - 4),
					Color.WHITE,
					2.0
				)
			
			# 绘制未命中
			if board.get_cell_state(x, y) == Board.CellState.MISS:
				draw_circle(
					Vector2((x + 0.5) * CELL_SIZE, (y + 0.5) * CELL_SIZE),
					CELL_SIZE * 0.3,
					MISS_COLOR
				)

# 船只大小列表
const SHIP_SIZES = [5, 4, 3, 3, 2]
var current_ship_index = 0

# 处理输入事件
func _gui_input(event):
# 修改后（使用tab缩进）
	if event is InputEventMouseButton:
		print_verbose("Mouse Button Event!")
	elif event is InputEventMouseMotion:
		print_verbose("Mouse Motion Event!")

	if not game or not board:
		return
	
	print_verbose("Input event received: %s" % event)
		
	if event is InputEventMouseButton and event.pressed and event.button_index == MOUSE_BUTTON_LEFT:
		print_verbose("Left mouse button pressed at: %s" % event.position)
		var cell = pixel_to_cell(event.position)
		var cell_i = Vector2i(cell.x, cell.y)
		print_verbose("Converted to cell: %s" % cell_i)
		
		if cell.x >= 0 and cell.x < BOARD_SIZE and cell.y >= 0 and cell.y < BOARD_SIZE:
			# 发射单元格点击信号
			print_verbose("Emitting cell_clicked signal for cell: %s" % cell_i)
			cell_clicked.emit(Vector2i(cell.x, cell.y))
			
			if is_player_board:
				# 玩家放置船只
				if game.current_state == Game.GameState.SETUP:
					if current_ship_index < SHIP_SIZES.size():
						# 尝试放置船只
						var ship_size = SHIP_SIZES[current_ship_index]
						if game.player_place_ship(ship_size, cell_i.x, cell_i.y, true):  # 默认水平放置
							# 更新显示
							update_display(game.get_player_board(), true)
							current_ship_index += 1
			else:
				# 玩家攻击AI的棋盘
				if game.current_state == Game.GameState.PLAYER_TURN:
					# 尝试攻击
					var result = game.player_attack(cell_i)
					if not result.is_empty():
						# 更新显示
						update_display(game.get_ai_board(), false)

# 获取鼠标悬停的格子坐标
func get_hover_cell() -> Vector2:
	var mouse_pos = get_local_mouse_position()
	var cell_x = int(mouse_pos.x / CELL_SIZE)
	var cell_y = int(mouse_pos.y / CELL_SIZE)
	return Vector2(cell_x, cell_y)

# 获取指定坐标的单元格状态
func get_cell(x: int, y: int) -> Dictionary:
	if not board or x < 0 or x >= BOARD_SIZE or y < 0 or y >= BOARD_SIZE:
		return {}
	
	return {
		"has_ship": board.has_ship_at(x, y),
		"is_hit": board.is_hit(x, y),
		"is_miss": board.is_miss(x, y),
		"position": Vector2(x * CELL_SIZE, y * CELL_SIZE),
		"size": Vector2(CELL_SIZE, CELL_SIZE)
	}

# 将格子坐标转换为像素坐标
func cell_to_pixel(cell: Vector2) -> Vector2:
	return Vector2(cell.x * CELL_SIZE, cell.y * CELL_SIZE)

# 将像素坐标转换为格子坐标
func pixel_to_cell(pixel: Vector2) -> Vector2:
	return Vector2(int(pixel.x / CELL_SIZE), int(pixel.y / CELL_SIZE))
