[remap]

importer="font_data_dynamic"
type="FontFile"
uid="uid://28pur6njodmr"
path="res://.godot/imported/Inter-Regular.ttf-36a9c8b22d869ae3915e0d29541fbf66.fontdata"

[deps]

source_file="res://resources/fonts/Inter-Regular.ttf"
dest_files=["res://.godot/imported/Inter-Regular.ttf-36a9c8b22d869ae3915e0d29541fbf66.fontdata"]

[params]

Rendering=null
antialiasing=1
generate_mipmaps=false
disable_embedded_bitmaps=true
multichannel_signed_distance_field=false
msdf_pixel_range=8
msdf_size=48
allow_system_fallback=true
force_autohinter=false
hinting=1
subpixel_positioning=4
keep_rounding_remainders=true
oversampling=0.0
Fallbacks=null
fallbacks=[]
Compress=null
compress=true
preload=[]
language_support={}
script_support={}
opentype_features={}
