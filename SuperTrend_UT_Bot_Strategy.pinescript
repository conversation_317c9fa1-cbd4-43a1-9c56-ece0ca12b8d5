//@version=6
strategy('SuperTrend + UT Bot 策略', overlay = true, default_qty_type = strategy.percent_of_equity, default_qty_value = 100)

//推荐设置

//日线级别：SuperTrend(12,2.5) + UT Bot(1.5,10)
//4小时级别：SuperTrend(10,2.0) + UT Bot(1.0,8)
//1小时级别：SuperTrend(8,1.8) + UT Bot(0.8,6)

// ==================== 参数设置 ====================
// SuperTrend 参数
st_period = input.int(12, 'SuperTrend 周期', minval = 1, maxval = 100, group = 'SuperTrend 设置')
st_multiplier = input.float(2.5, 'SuperTrend 乘数', minval = 0.01, maxval = 10, step = 0.01, group = 'SuperTrend 设置')

// UT Bot 参数
ut_key_value = input.float(1.5, 'UT Bot 关键值', minval = 0.1, maxval = 5, step = 0.1, group = 'UT Bot 设置')
ut_atr_period = input.int(10, 'UT Bot ATR 周期', minval = 1, maxval = 50, group = 'UT Bot 设置')

// 风险管理参数
use_stop_loss = input.bool(true, '启用止损', group = '风险管理')
stop_loss_pct = input.float(2.0, '止损百分比 %', minval = 0.1, maxval = 10, step = 0.1, group = '风险管理')
use_take_profit = input.bool(true, '启用止盈', group = '风险管理')
take_profit_pct = input.float(4.0, '止盈百分比 %', minval = 0.1, maxval = 20, step = 0.1, group = '风险管理')

// 交易时间过滤
use_time_filter = input.bool(false, '启用时间过滤', group = '时间过滤')
start_time = input.time(timestamp('2023-01-01T00:00:00'), '开始时间', group = '时间过滤')
end_time = input.time(timestamp('2024-12-31T23:59:59'), '结束时间', group = '时间过滤')

// ==================== SuperTrend 计算 ====================
atr = ta.atr(st_period)
hl2 = (high + low) / 2
upper_band = hl2 + st_multiplier * atr
lower_band = hl2 - st_multiplier * atr

var float supertrend = na
var int direction = 1

if na(supertrend[1])
    supertrend := lower_band
    direction := 1
    direction
else
    if close[1] <= supertrend[1]
        supertrend := upper_band
        direction := -1
        direction
    else
        supertrend := lower_band
        direction := 1
        direction

    if direction == -1 and supertrend > supertrend[1]
        supertrend := supertrend[1]
        supertrend
    if direction == 1 and supertrend < supertrend[1]
        supertrend := supertrend[1]
        supertrend

// SuperTrend 趋势方向
is_uptrend = direction == 1
is_downtrend = direction == -1

// ==================== UT Bot 计算 ====================
ut_atr = ta.atr(ut_atr_period)
ut_hl2 = (high + low) / 2

// UT Bot 上下轨
ut_upper = ut_hl2 + ut_key_value * ut_atr
ut_lower = ut_hl2 - ut_key_value * ut_atr

var float ut_trail = na
var int ut_dir = 1

if na(ut_trail[1])
    ut_trail := ut_lower
    ut_dir := 1
    ut_dir
else
    if close > ut_trail[1]
        ut_trail := ut_lower
        ut_dir := 1
        ut_dir
    else if close < ut_trail[1]
        ut_trail := ut_upper
        ut_dir := -1
        ut_dir
    else
        ut_trail := ut_trail[1]
        ut_dir := ut_dir[1]
        ut_dir

// UT Bot 信号
ut_buy_signal = ut_dir == 1 and ut_dir[1] == -1
ut_sell_signal = ut_dir == -1 and ut_dir[1] == 1

// ==================== 交易条件 ====================
// 时间过滤
time_cond = use_time_filter ? time >= start_time and time <= end_time : true

// 做多条件：SuperTrend上升趋势 + UT Bot买入信号
long_condition = is_uptrend and ut_buy_signal and time_cond

// 做空条件：SuperTrend下降趋势 + UT Bot卖出信号  
short_condition = is_downtrend and ut_sell_signal and time_cond

// 平仓条件
close_long = is_downtrend or ut_sell_signal
close_short = is_uptrend or ut_buy_signal

// ==================== 执行交易 ====================
if long_condition
    strategy.entry('做多', strategy.long)

if short_condition
    strategy.entry('做空', strategy.short)

if close_long
    strategy.close('做多')

if close_short
    strategy.close('做空')

// ==================== 止损止盈 ====================
if use_stop_loss
    strategy.exit('多头止损', '做多', stop = close * (1 - stop_loss_pct / 100))
    strategy.exit('空头止损', '做空', stop = close * (1 + stop_loss_pct / 100))

if use_take_profit
    strategy.exit('多头止盈', '做多', limit = close * (1 + take_profit_pct / 100))
    strategy.exit('空头止盈', '做空', limit = close * (1 - take_profit_pct / 100))

// ==================== 图表显示 ====================
// SuperTrend 线
plot(supertrend, title = 'SuperTrend 线', color = is_uptrend ? color.green : color.red, linewidth = 2)

// UT Bot 轨迹线
plot(ut_trail, title = 'UT Bot 轨迹线', color = ut_dir == 1 ? color.blue : color.orange, linewidth = 1)

// 买卖信号
plotshape(ut_buy_signal and is_uptrend, title = '买入信号', location = location.belowbar, style = shape.labelup, size = size.small, color = color.green, text = '买入')
plotshape(ut_sell_signal and is_downtrend, title = '卖出信号', location = location.abovebar, style = shape.labeldown, size = size.small, color = color.red, text = '卖出')

// 背景颜色显示趋势
bgcolor(is_uptrend ? color.new(color.green, 95) : color.new(color.red, 95), title = '趋势背景')

// ==================== 信息面板 ====================
var table info_table = table.new(position.top_right, 2, 6, bgcolor = color.white, border_width = 1)

if barstate.islast
    table.cell(info_table, 0, 0, 'SuperTrend', text_color = color.black, text_size = size.small)
    table.cell(info_table, 1, 0, is_uptrend ? '上升' : '下降', text_color = is_uptrend ? color.green : color.red, text_size = size.small)

    table.cell(info_table, 0, 1, 'UT Bot', text_color = color.black, text_size = size.small)
    table.cell(info_table, 1, 1, ut_dir == 1 ? '看多' : '看空', text_color = ut_dir == 1 ? color.blue : color.orange, text_size = size.small)

    table.cell(info_table, 0, 2, '当前价格', text_color = color.black, text_size = size.small)
    table.cell(info_table, 1, 2, str.tostring(close, '#.##'), text_color = color.black, text_size = size.small)

    table.cell(info_table, 0, 3, 'SuperTrend值', text_color = color.black, text_size = size.small)
    table.cell(info_table, 1, 3, str.tostring(supertrend, '#.##'), text_color = color.black, text_size = size.small)

    table.cell(info_table, 0, 4, 'UT 轨迹值', text_color = color.black, text_size = size.small)
    table.cell(info_table, 1, 4, str.tostring(ut_trail, '#.##'), text_color = color.black, text_size = size.small)

    table.cell(info_table, 0, 5, '信号状态', text_color = color.black, text_size = size.small)
    signal_text = long_condition ? '做多' : short_condition ? '做空' : '等待'
    signal_color = long_condition ? color.green : short_condition ? color.red : color.gray
    table.cell(info_table, 1, 5, signal_text, text_color = signal_color, text_size = size.small)

// ==================== 警报条件 ====================
alertcondition(long_condition, title = '做多信号', message = 'SuperTrend上升趋势 + UT Bot买入信号')
alertcondition(short_condition, title = '做空信号', message = 'SuperTrend下降趋势 + UT Bot卖出信号')
alertcondition(close_long, title = '平多仓', message = '趋势转换或UT Bot卖出信号')
alertcondition(close_short, title = '平空仓', message = '趋势转换或UT Bot买入信号')
