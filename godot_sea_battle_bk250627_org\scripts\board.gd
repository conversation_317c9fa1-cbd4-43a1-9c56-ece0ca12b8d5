extends RefCounted
class_name Board

const Ship = preload("res://scripts/ship.gd")
## 游戏棋盘
##
## 管理船只放置、攻击处理和状态追踪
## @version: 4.4.1

# 单元格状态枚举
enum CellState {
	EMPTY,
	HIT,
	MISS,
	SHIP,
	SUNK
}

# 棋盘大小
const BOARD_SIZE = 10

# 船只列表
var ships: Array[Ship] = []
# 单元格状态
var cell_states: Array[Array] = []

# 初始化
func _init():
	# 初始化单元格状态
	cell_states = []
	for y in range(BOARD_SIZE):
		var row = []
		for x in range(BOARD_SIZE):
			row.append(CellState.EMPTY)
		cell_states.append(row)

# 获取单元格状态
func get_cell_state(x: int, y: int) -> int:
	if x >= 0 and x < BOARD_SIZE and y >= 0 and y < BOARD_SIZE:
		return cell_states[y][x]
	return CellState.EMPTY

# 设置单元格状态
func set_cell_state(x: int, y: int, state: int) -> void:
	if x >= 0 and x < BOARD_SIZE and y >= 0 and y < BOARD_SIZE:
		cell_states[y][x] = state

# 检查坐标是否在棋盘内
func is_valid_position(x: int, y: int) -> bool:
	return x >= 0 and x < BOARD_SIZE and y >= 0 and y < BOARD_SIZE

# 检查是否可以放置船只
func can_place_ship(ship_size: int, x: int, y: int, is_horizontal: bool) -> bool:
	# 检查船只是否超出棋盘
	if is_horizontal:
		if x + ship_size > BOARD_SIZE:
			push_warning("放置失败: 船只超出棋盘右边界")
			return false
	else:
		if y + ship_size > BOARD_SIZE:
			push_warning("放置失败: 船只超出棋盘下边界")
			return false
	
	# 检查是否与其他船只重叠或相邻
	for i in range(ship_size):
		var check_x = x + (i if is_horizontal else 0)
		var check_y = y + (i if not is_horizontal else 0)
		
		# 检查当前位置
		if has_ship_at(check_x, check_y):
			push_warning("放置失败: 位置 (%d, %d) 已有船只" % [check_x, check_y])
			return false
		
		# 检查周围8个方向
		for dx in range(-1, 2):
			for dy in range(-1, 2):
				if dx == 0 and dy == 0:
					continue
				
				var nx = check_x + dx
				var ny = check_y + dy
				
				if is_valid_position(nx, ny) and has_ship_at(nx, ny):
					push_warning("放置失败: 位置 (%d, %d) 与位置 (%d, %d) 的船只相邻" % [check_x, check_y, nx, ny])
					return false
	
	return true

# 尝试放置船只（与Game类兼容的接口）
func try_place_ship(ship_size: int, x: int, y: int, horizontal: bool) -> bool:
	print_verbose("尝试放置船只: 大小=%d 位置=(%d, %d) 水平=%s" % [ship_size, x, y, horizontal])
	return place_ship(ship_size, x, y, horizontal)

# 放置船只
func place_ship(ship_size: int, x: int, y: int, is_horizontal: bool) -> bool:
	# 检查船只数量限制
	var ship_counts = {2: 0, 3: 0, 4: 0, 5: 0}
	for ship in ships:
		if ship.size in ship_counts:
			ship_counts[ship.size] += 1
	
	# 检查是否超过限制
	var max_counts = {2: 1, 3: 2, 4: 1, 5: 1}
	if ship_size in ship_counts and ship_counts[ship_size] >= max_counts[ship_size]:
		push_warning("%d格船已达到最大数量 (%d/%d)" % [ship_size, ship_counts[ship_size], max_counts[ship_size]])
		return false
	
	if not can_place_ship(ship_size, x, y, is_horizontal):
		push_warning("放置失败: 位置无效")
		return false
	
	# 创建新船只
	var ship = Ship.new(ship_size, x, y, is_horizontal)
	ships.append(ship)
	
	# 更新单元格状态
	for i in range(ship_size):
		var ship_x = x + (i if is_horizontal else 0)
		var ship_y = y + (i if not is_horizontal else 0)
		set_cell_state(ship_x, ship_y, CellState.SHIP)
	
	print_verbose("成功放置船只: 大小=%d 位置=(%d, %d) 水平=%s" % [ship_size, x, y, is_horizontal])
	
	return true

# 检查指定位置是否有船只
func has_ship_at(x: int, y: int) -> bool:
	for ship in ships:
		if ship.is_at_position(x, y):
			return true
	return false

# 获取指定位置的船只
func get_ship_at(x: int, y: int) -> Ship:
	for ship in ships:
		if ship.is_at_position(x, y):
			return ship
	return null

# 检查是否可以攻击指定位置（与Game类兼容的接口）
func is_valid_attack_position(position: Vector2i) -> bool:
	return is_valid_position(position.x, position.y) and get_cell_state(position.x, position.y) == CellState.EMPTY

# 处理攻击（与Game类兼容的接口）
func take_hit(position: Vector2i) -> Dictionary:
	var result: Dictionary = {
		"hit": false,
		"sunk": false,
		"ship_type": StringName("")
	}
	
	if attack(position.x, position.y):
		var ship = get_ship_at(position.x, position.y)
		if ship:
			result.hit = true
			if ship.is_sunk():
				result.sunk = true
				result.ship_type = StringName("ship_" + str(ship.size))
	
	return result

# 攻击指定位置
func attack(x: int, y: int) -> bool:
	# 检查是否已经攻击过
	if get_cell_state(x, y) != CellState.EMPTY:
		return false
	
	# 检查是否击中船只
	if has_ship_at(x, y):
		set_cell_state(x, y, CellState.HIT)
		
		# 获取被击中的船只
		var ship = get_ship_at(x, y)
		ship.hit()
		
		return true
	else:
		set_cell_state(x, y, CellState.MISS)
		return true

# 检查所有船只是否都被击沉
var all_ships_sunk: bool:
	get:
		for ship in ships:
			if not ship.is_sunk():
				return false
		return true

# 检查是否所有船只都已放置
var all_ships_placed: bool:
	get:
		# 检查是否已放置所有船只
		var ship_counts = {
			2: 0,
			3: 0,
			4: 0,
			5: 0
		}
		
		for ship in ships:
			if ship.size in ship_counts:
				ship_counts[ship.size] += 1
		
		return ship_counts[2] == 1 and ship_counts[3] == 2 and ship_counts[4] == 1 and ship_counts[5] == 1

# 自动放置所有船只
func auto_place_ships() -> bool:
	# 清除现有船只
	ships.clear()
	
	# 要放置的船只大小
	var ship_sizes = [5, 4, 3, 3, 2]
	
	# 尝试放置每艘船
	for size in ship_sizes:
		var placed = false
		var attempts = 0
		
		# 最多尝试100次
		while not placed and attempts < 100:
			var x = randi() % BOARD_SIZE
			var y = randi() % BOARD_SIZE
			var is_horizontal = randi() % 2 == 0
			
			if can_place_ship(size, x, y, is_horizontal):
				place_ship(size, x, y, is_horizontal)
				placed = true
			
			attempts += 1
		
		# 如果无法放置某艘船，返回失败
		if not placed:
			return false
	
	return true

# 比较两个棋盘是否相同
func is_same_as(other: Board) -> bool:
	# 检查船只数量
	if ships.size() != other.ships.size():
		return false
	
	# 检查每艘船的位置和方向
	for i in range(ships.size()):
		var my_ship = ships[i]
		var other_ship = other.ships[i]
		
		if my_ship.size != other_ship.size or \
		   my_ship.x != other_ship.x or \
		   my_ship.y != other_ship.y or \
		   my_ship.is_horizontal != other_ship.is_horizontal:
			return false
	
	# 检查单元格状态
	for y in range(BOARD_SIZE):
		for x in range(BOARD_SIZE):
			if get_cell_state(x, y) != other.get_cell_state(x, y):
				return false
	
	return true

# 打印盘面状态(调试用)
func print_board_state():
	print("\n=== 盘面详细信息 ===")
	_print_ship_info()
	print("\n单元格状态:")
	for y in range(BOARD_SIZE):
		var row = "|"
		for x in range(BOARD_SIZE):
			var cell = get_cell_state(x, y)
			if cell == CellState.SHIP:
				row += "船|"
			elif cell == CellState.HIT:
				row += "中|"
			elif cell == CellState.MISS:
				row += "空|"
			elif cell == CellState.SUNK:
				row += "沉|"
			else:
				row += "  |"
		print(row)
	print("图例: 船=船舶位置, 中=击中, 空=未击中, 沉=击沉")

# 打印船舶信息
func _print_ship_info():
	print("\n船舶列表:")
	for ship in ships:
		var dir = "水平" if ship.horizontal else "垂直"
		var status = "存活" if !ship.is_sunk() else "已击沉"
		print("大小: %d, 位置: (%d,%d), 方向: %s, 状态: %s" % [
			ship.size, ship.x, ship.y, dir, status
		])

# 获取剩余可放置的船只数量
func get_remaining_ships() -> Dictionary:
	var ship_counts = {2: 0, 3: 0, 4: 0, 5: 0}
	for ship in ships:
		if ship.size in ship_counts:
			ship_counts[ship.size] += 1
	
	var max_counts = {2: 1, 3: 2, 4: 1, 5: 1}
	var remaining = {}
	
	for size in ship_counts:
		remaining[size] = max_counts[size] - ship_counts[size]
	
	return remaining

# 重置棋盘
func reset() -> void:
	# 清除船只
	ships.clear()
	
	# 重置单元格状态
	for y in range(BOARD_SIZE):
		for x in range(BOARD_SIZE):
			cell_states[y][x] = CellState.EMPTY
