//@version=5
strategy("Breakout Pro+ Strategy", overlay=true, default_qty_type=strategy.percent_of_equity, default_qty_value=10)

// === 用户参数 ===
length       = input.int(20, title="Breakout Lookback")
ema_fast     = input.int(50, title="Fast EMA")
ema_slow     = input.int(200, title="Slow EMA (Trend Filter)")
htf_tf       = input.timeframe("60", title="Higher Timeframe")
use_htf      = input.bool(true, title="Use Higher Timeframe Trend Filter?")
vol_len      = input.int(20, title="Volume SMA Length")
vol_ratio    = input.float(1.5, title="Volume Spike Threshold")
trailing_tp  = input.float(1.5, title="Trailing TP (% of entry)")
max_trades   = input.int(1, title="Max Simultaneous Trades", minval=1)
direction    = input.string("Both", options=["Both", "Long Only", "Short Only"], title="Trade Direction")

// === 多周期趋势过滤 ===
ema_slow_htf = request.security(syminfo.tickerid, htf_tf, ta.ema(close, ema_slow))
ema_fast_htf = request.security(syminfo.tickerid, htf_tf, ta.ema(close, ema_fast))
htf_uptrend  = ema_fast_htf > ema_slow_htf
htf_dntrend  = ema_fast_htf < ema_slow_htf

// === 当前周期趋势过滤 ===
ema_fast_cur = ta.ema(close, ema_fast)
ema_slow_cur = ta.ema(close, ema_slow)
trend_up     = ema_fast_cur > ema_slow_cur
trend_down   = ema_fast_cur < ema_slow_cur

// === 成交量过滤 ===
vol_avg  = ta.sma(volume, vol_len)
vol_valid = volume > vol_avg * vol_ratio

// === 突破信号 ===
high_lv = ta.highest(high, length)
low_lv  = ta.lowest(low, length)
break_up = ta.crossover(close, high_lv)
break_dn = ta.crossunder(close, low_lv)

// === 仓位检测 ===
in_long  = strategy.position_size > 0
in_short = strategy.position_size < 0

// === 多空方向控制 ===
allow_long  = (direction == "Both" or direction == "Long Only")
allow_short = (direction == "Both" or direction == "Short Only")

// === 多头信号 ===
long_cond = break_up and trend_up and vol_valid and allow_long and (not in_long) and (strategy.opentrades < max_trades)
if use_htf
    long_cond := long_cond and htf_uptrend

if (long_cond)
    strategy.entry("Long", strategy.long, comment="Breakout Long")
    stop_price = low[length] // 结构止损点
    trail_amount = close * trailing_tp / 100
    strategy.exit("Exit Long", from_entry="Long", stop=stop_price, trail_points=trail_amount/ syminfo.mintick)

// === 空头信号 ===
short_cond = break_dn and trend_down and vol_valid and allow_short and (not in_short) and (strategy.opentrades < max_trades)
if use_htf
    short_cond := short_cond and htf_dntrend

if (short_cond)
    strategy.entry("Short", strategy.short, comment="Breakout Short")
    stop_price = high[length]
    trail_amount = close * trailing_tp / 100
    strategy.exit("Exit Short", from_entry="Short", stop=stop_price, trail_points=trail_amount/ syminfo.mintick)

// === 可视化辅助 ===
plot(high_lv, color=color.green, title="Breakout High", linewidth=2)
plot(low_lv, color=color.red, title="Breakout Low", linewidth=2)
plot(ema_fast_cur, color=color.orange, title="EMA Fast", linewidth=2)
plot(ema_slow_cur, color=color.blue, title="EMA Slow", linewidth=2)
