//@version=6
indicator(title = 'go6 UT Bot Alerts 超级趋势（Supertrend）指标', overlay = true)
//indicator(title = 'UT Bot Alerts', overlay = true)

// Inputs
a = input(1, title = '<PERSON> Vaule. \'This changes the sensitivity\'')
c = input(10, title = 'ATR Period')
h = input(false, title = 'Signals from Heikin Ashi Candles')

xATR = ta.atr(c)
nLoss = a * xATR

src = h ? request.security(ticker.heikinashi(syminfo.tickerid), timeframe.period, close, lookahead = barmerge.lookahead_off) : close

xATRTrailingStop = 0.0
iff_1 = src > nz(xATRTrailingStop[1], 0) ? src - nLoss : src + nLoss
iff_2 = src < nz(xATRTrailingStop[1], 0) and src[1] < nz(xATRTrailingStop[1], 0) ? math.min(nz(xATRTrailingStop[1]), src + nLoss) : iff_1
xATRTrailingStop := src > nz(xATRTrailingStop[1], 0) and src[1] > nz(xATRTrailingStop[1], 0) ? math.max(nz(xATRTrailingStop[1]), src - nLoss) : iff_2

pos = 0
iff_3 = src[1] > nz(xATRTrailingStop[1], 0) and src < nz(xATRTrailingStop[1], 0) ? -1 : nz(pos[1], 0)
pos := src[1] < nz(xATRTrailingStop[1], 0) and src > nz(xATRTrailingStop[1], 0) ? 1 : iff_3

xcolor = pos == -1 ? color.red : pos == 1 ? color.green : color.blue

ema = ta.ema(src, 1)
above = ta.crossover(ema, xATRTrailingStop)
below = ta.crossover(xATRTrailingStop, ema)

buy = src > xATRTrailingStop and above
sell = src < xATRTrailingStop and below

barbuy = src > xATRTrailingStop
barsell = src < xATRTrailingStop

plotshape(buy, title = 'Buy', text = '买', style = shape.labelup, location = location.belowbar, color = color.new(color.green, 0), textcolor = color.new(color.white, 0), size = size.tiny)
plotshape(sell, title = 'Sell', text = '卖', style = shape.labeldown, location = location.abovebar, color = color.new(color.red, 0), textcolor = color.new(color.white, 0), size = size.tiny)

barcolor(barbuy ? color.green : na)
barcolor(barsell ? color.red : na)

alertcondition(buy, 'UT Long', 'UT Long')
alertcondition(sell, 'UT Short', 'UT Short')

// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © samuelx

//@version=6
//indicator(title = '超级趋势（Supertrend）指标', shorttitle = '超级趋势指标', overlay = true)

//设置使用的价格源，包括不同的价格类型
src2 = input(hl2, title = '价格源')

//设置使用的ATR参数
Periods = input(title = 'ATR参数', defval = 10)


//设置ATR的乘数，默认值：3
Multiplier = input.float(title = 'ATR乘数', step = 0.1, defval = 3.0)

//定义是否改变ATR计算方式的开关，默认打开
changeATR = input(title = '改变ATR的计算方式 ?', defval = true)

//定义是否显示买卖信号点的开关，默认打开，即默认显示买卖信号点
showsignals = input(title = '显示买卖信号点 ?', defval = true)

//定义是否高亮显示趋势的开关，默认打开，即默认高亮显示趋势
highlighting = input(title = '趋势高亮显示开关 ?', defval = true)


//一种真实波动幅度的计算公式
atr2 = ta.sma(ta.tr, Periods)

//选择真实波动幅度计算方式的条件判断语句，默认使用常规真是波动幅度计算方式。
//可以在指标设置中通过取消勾选“改变ATR的计算方式？”选项换用另一种真实波动幅度的计算方式。
atr = changeATR ? ta.atr(Periods) : atr2

//计算超级趋势指标的上涨趋势线
up = src2 - Multiplier * atr
up1 = nz(up[1], up)
up := close[1] > up1 ? math.max(up, up1) : up

//计算超级趋势指标的下跌趋势线
dn = src2 + Multiplier * atr
dn1 = nz(dn[1], dn)
dn := close[1] < dn1 ? math.min(dn, dn1) : dn

//定义趋势方向
trend = 1
trend := nz(trend[1], trend)
trend := trend == -1 and close > dn1 ? 1 : trend == 1 and close < up1 ? -1 : trend

//在图表上绘出超级趋势指标的上升趋势线
upPlot = plot(trend == 1 ? up : na, title = '上升趋势', style = plot.style_linebr, linewidth = 2, color = color.new(color.green, 0))
//定义买入信号
buySignal = trend == 1 and trend[1] == -1
//在图表上绘出超级趋势指标上升趋势开始时的信号点
plotshape(buySignal ? up : na, title = '上升趋势开始', location = location.absolute, style = shape.circle, size = size.tiny, color = color.new(color.green, 0))
plotshape(buySignal and showsignals ? up : na, title = '买入信号标示', text = 'Buy', location = location.absolute, style = shape.labelup, size = size.tiny, color = color.new(color.green, 0), textcolor = color.new(color.white, 0))

//在图表上绘出超级趋势指标的下降趋势线
dnPlot = plot(trend == 1 ? na : dn, title = '下降趋势', style = plot.style_linebr, linewidth = 2, color = color.new(color.red, 0))
//定义卖出信号
sellSignal = trend == -1 and trend[1] == 1
//在图表上绘出超级趋势指标下降趋势开始时的信号点
plotshape(sellSignal ? dn : na, title = '下降趋势开始', location = location.absolute, style = shape.circle, size = size.tiny, color = color.new(color.red, 0))
plotshape(sellSignal and showsignals ? dn : na, title = '卖出信号标示', text = 'Sell', location = location.absolute, style = shape.labeldown, size = size.tiny, color = color.new(color.red, 0), textcolor = color.new(color.white, 0))

//
mPlot = plot(ohlc4, title = 'ohlc4价格', style = plot.style_circles, linewidth = math.max(1, math.max(1, 0)))

//定义上升趋势高亮显示的颜色
longFillColor = highlighting ? trend == 1 ? color.rgb(76, 175, 79, 85) : color.white : color.white
//在图表上绘出上升趋势的高亮显示
shortFillColor = highlighting ? trend == -1 ? color.rgb(255, 82, 82, 84) : color.white : color.white
fill(mPlot, upPlot, title = '上升趋势高亮显示区域', color = longFillColor)
//在图表上绘出下降趋势的高亮显示
fill(mPlot, dnPlot, title = '下降趋势高亮显示区域', color = shortFillColor)

//设置警报条件
//定义买入信号的报警条件
alertcondition(buySignal, title = '超级趋势指标买入信号', message = '超级趋势指标显示可以买入了！')
//定义卖出信号的报警条件
alertcondition(sellSignal, title = '超级趋势指标卖出信号', message = '超级趋势指标显示可以卖出了！')

//定义趋势方向发生改变
changeCond = trend != trend[1]
//定义趋势方向发生改变的报警条件
alertcondition(changeCond, title = '超级趋势指标的方向改变了', message = '超级趋势指标的方向改变了！')
