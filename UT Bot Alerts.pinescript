//@version=6
strategy(title = 'UT Bot + SuperTrend 双重趋势策略', shorttitle = 'UT+ST Strategy', overlay = true,
         default_qty_type = strategy.percent_of_equity, default_qty_value = 10,
         commission_type = strategy.commission.percent, commission_value = 0.1,
         slippage = 2, max_bars_back = 500)

// === 策略参数设置 ===
var g1 = "=== UT Bot 设置 ==="

// UT Bot 参数
a = input(1, title = 'Key Value (敏感度)', group = g1)
c = input(10, title = 'ATR Period (ATR周期)', group = g1)
h = input(false, title = 'Signals from Heikin Ashi Candles (使用平均K线)', group = g1)

var g2 = "=== SuperTrend 设置 ==="
// SuperTrend 参数 (将在后面定义)

var g3 = "=== 策略设置 ==="
signal_mode = input.string("双重确认", title = "信号模式", options = ["UT Bot", "SuperTrend", "双重确认", "任一信号"], group = g3)
use_stop_loss = input.bool(true, title = "启用止损", group = g3)
stop_loss_pct = input.float(2.0, title = "止损百分比 (%)", minval = 0.5, maxval = 10.0, group = g3)
use_take_profit = input.bool(true, title = "启用止盈", group = g3)
take_profit_pct = input.float(4.0, title = "止盈百分比 (%)", minval = 1.0, maxval = 20.0, group = g3)
use_trailing_stop = input.bool(true, title = "启用追踪止损", group = g3)
trailing_stop_pct = input.float(1.5, title = "追踪止损百分比 (%)", minval = 0.5, maxval = 5.0, group = g3)

var g4 = "=== 风险管理 ==="
max_trades = input.int(3, title = "最大同时持仓数", minval = 1, maxval = 10, group = g4)
trade_direction = input.string("双向", title = "交易方向", options = ["做多", "做空", "双向"], group = g4)
start_date = input(timestamp("2020-01-01"), title = "策略开始日期", group = g4)
end_date = input(timestamp("2025-12-31"), title = "策略结束日期", group = g4)

xATR = ta.atr(c)
nLoss = a * xATR

src = h ? request.security(ticker.heikinashi(syminfo.tickerid), timeframe.period, close, lookahead = barmerge.lookahead_off) : close

xATRTrailingStop = 0.0
iff_1 = src > nz(xATRTrailingStop[1], 0) ? src - nLoss : src + nLoss
iff_2 = src < nz(xATRTrailingStop[1], 0) and src[1] < nz(xATRTrailingStop[1], 0) ? math.min(nz(xATRTrailingStop[1]), src + nLoss) : iff_1
xATRTrailingStop := src > nz(xATRTrailingStop[1], 0) and src[1] > nz(xATRTrailingStop[1], 0) ? math.max(nz(xATRTrailingStop[1]), src - nLoss) : iff_2

pos = 0
iff_3 = src[1] > nz(xATRTrailingStop[1], 0) and src < nz(xATRTrailingStop[1], 0) ? -1 : nz(pos[1], 0)
pos := src[1] < nz(xATRTrailingStop[1], 0) and src > nz(xATRTrailingStop[1], 0) ? 1 : iff_3

xcolor = pos == -1 ? color.red : pos == 1 ? color.green : color.blue

ema = ta.ema(src, 1)
above = ta.crossover(ema, xATRTrailingStop)
below = ta.crossover(xATRTrailingStop, ema)

buy = src > xATRTrailingStop and above
sell = src < xATRTrailingStop and below

barbuy = src > xATRTrailingStop
barsell = src < xATRTrailingStop

plotshape(buy, title = 'Buy', text = '买', style = shape.labelup, location = location.belowbar, color = color.new(color.green, 0), textcolor = color.new(color.white, 0), size = size.tiny)
plotshape(sell, title = 'Sell', text = '卖', style = shape.labeldown, location = location.abovebar, color = color.new(color.red, 0), textcolor = color.new(color.white, 0), size = size.tiny)

barcolor(barbuy ? color.green : na)
barcolor(barsell ? color.red : na)

alertcondition(buy, 'UT Long', 'UT Long')
alertcondition(sell, 'UT Short', 'UT Short')

// This source code is subject to the terms of the Mozilla Public License 2.0 at https://mozilla.org/MPL/2.0/
// © samuelx

//@version=6
//indicator(title = '超级趋势（Supertrend）指标', shorttitle = '超级趋势指标', overlay = true)

// SuperTrend 参数
src2 = input(hl2, title = '价格源', group = g2)
Periods = input(title = 'ATR参数', defval = 10, group = g2)
Multiplier = input.float(title = 'ATR乘数', step = 0.1, defval = 3.0, group = g2)
changeATR = input(title = '改变ATR的计算方式 ?', defval = true, group = g2)
showsignals = input(title = '显示买卖信号点 ?', defval = true, group = g2)
highlighting = input(title = '趋势高亮显示开关 ?', defval = true, group = g2)

// === 时间过滤 ===
in_date_range = time >= start_date and time <= end_date


//一种真实波动幅度的计算公式
atr2 = ta.sma(ta.tr, Periods)

//选择真实波动幅度计算方式的条件判断语句，默认使用常规真是波动幅度计算方式。
//可以在指标设置中通过取消勾选“改变ATR的计算方式？”选项换用另一种真实波动幅度的计算方式。
atr = changeATR ? ta.atr(Periods) : atr2

//计算超级趋势指标的上涨趋势线
up = src2 - Multiplier * atr
up1 = nz(up[1], up)
up := close[1] > up1 ? math.max(up, up1) : up

//计算超级趋势指标的下跌趋势线
dn = src2 + Multiplier * atr
dn1 = nz(dn[1], dn)
dn := close[1] < dn1 ? math.min(dn, dn1) : dn

//定义趋势方向
trend = 1
trend := nz(trend[1], trend)
trend := trend == -1 and close > dn1 ? 1 : trend == 1 and close < up1 ? -1 : trend

//在图表上绘出超级趋势指标的上升趋势线
upPlot = plot(trend == 1 ? up : na, title = '上升趋势', style = plot.style_linebr, linewidth = 2, color = color.new(color.green, 0))
//定义买入信号
buySignal = trend == 1 and trend[1] == -1
//在图表上绘出超级趋势指标上升趋势开始时的信号点
plotshape(buySignal ? up : na, title = '上升趋势开始', location = location.absolute, style = shape.circle, size = size.tiny, color = color.new(color.green, 0))
plotshape(buySignal and showsignals ? up : na, title = '买入信号标示', text = 'Buy', location = location.absolute, style = shape.labelup, size = size.tiny, color = color.new(color.green, 0), textcolor = color.new(color.white, 0))

//在图表上绘出超级趋势指标的下降趋势线
dnPlot = plot(trend == 1 ? na : dn, title = '下降趋势', style = plot.style_linebr, linewidth = 2, color = color.new(color.red, 0))
//定义卖出信号
sellSignal = trend == -1 and trend[1] == 1
//在图表上绘出超级趋势指标下降趋势开始时的信号点
plotshape(sellSignal ? dn : na, title = '下降趋势开始', location = location.absolute, style = shape.circle, size = size.tiny, color = color.new(color.red, 0))
plotshape(sellSignal and showsignals ? dn : na, title = '卖出信号标示', text = 'Sell', location = location.absolute, style = shape.labeldown, size = size.tiny, color = color.new(color.red, 0), textcolor = color.new(color.white, 0))

//
mPlot = plot(ohlc4, title = 'ohlc4价格', style = plot.style_circles, linewidth = math.max(1, math.max(1, 0)))

//定义上升趋势高亮显示的颜色
longFillColor = highlighting ? trend == 1 ? color.rgb(76, 175, 79, 85) : color.white : color.white
//在图表上绘出上升趋势的高亮显示
shortFillColor = highlighting ? trend == -1 ? color.rgb(255, 82, 82, 84) : color.white : color.white
fill(mPlot, upPlot, title = '上升趋势高亮显示区域', color = longFillColor)
//在图表上绘出下降趋势的高亮显示
fill(mPlot, dnPlot, title = '下降趋势高亮显示区域', color = shortFillColor)

//设置警报条件
//定义买入信号的报警条件
alertcondition(buySignal, title = '超级趋势指标买入信号', message = '超级趋势指标显示可以买入了！')
//定义卖出信号的报警条件
alertcondition(sellSignal, title = '超级趋势指标卖出信号', message = '超级趋势指标显示可以卖出了！')

//定义趋势方向发生改变
changeCond = trend != trend[1]
//定义趋势方向发生改变的报警条件
alertcondition(changeCond, title = '超级趋势指标的方向改变了', message = '超级趋势指标的方向改变了！')

// ===========================
// 策略交易逻辑
// ===========================

// === 信号组合逻辑 ===
ut_buy_signal = buy
ut_sell_signal = sell
st_buy_signal = buySignal
st_sell_signal = sellSignal

// 根据信号模式确定最终交易信号
final_buy_signal = switch signal_mode
    "UT Bot" => ut_buy_signal
    "SuperTrend" => st_buy_signal
    "双重确认" => ut_buy_signal and st_buy_signal
    "任一信号" => ut_buy_signal or st_buy_signal
    => false

final_sell_signal = switch signal_mode
    "UT Bot" => ut_sell_signal
    "SuperTrend" => st_sell_signal
    "双重确认" => ut_sell_signal and st_sell_signal
    "任一信号" => ut_sell_signal or st_sell_signal
    => false

// === 仓位管理 ===
in_long = strategy.position_size > 0
in_short = strategy.position_size < 0
can_trade = in_date_range and strategy.opentrades < max_trades

// 交易方向控制
allow_long = trade_direction == "做多" or trade_direction == "双向"
allow_short = trade_direction == "做空" or trade_direction == "双向"

// === 入场条件 ===
long_condition = final_buy_signal and allow_long and not in_long and can_trade
short_condition = final_sell_signal and allow_short and not in_short and can_trade

// === 出场条件 ===
// 反向信号出场
long_exit_signal = final_sell_signal and in_long
short_exit_signal = final_buy_signal and in_short

// 趋势线出场 (使用SuperTrend线作为趋势出场)
long_trend_exit = in_long and close < (trend == 1 ? up : dn)
short_trend_exit = in_short and close > (trend == -1 ? dn : up)

// === 策略执行 ===
if long_condition
    strategy.entry("Long", strategy.long, comment="双重买入")

    // 设置止损止盈
    if use_stop_loss
        stop_price = close * (1 - stop_loss_pct / 100)
        strategy.exit("Long SL", from_entry="Long", stop=stop_price)

    if use_take_profit
        tp_price = close * (1 + take_profit_pct / 100)
        strategy.exit("Long TP", from_entry="Long", limit=tp_price)

    if use_trailing_stop
        trail_amount = close * trailing_stop_pct / 100
        strategy.exit("Long Trail", from_entry="Long", trail_points=trail_amount/syminfo.mintick)

if short_condition
    strategy.entry("Short", strategy.short, comment="双重卖出")

    // 设置止损止盈
    if use_stop_loss
        stop_price = close * (1 + stop_loss_pct / 100)
        strategy.exit("Short SL", from_entry="Short", stop=stop_price)

    if use_take_profit
        tp_price = close * (1 - take_profit_pct / 100)
        strategy.exit("Short TP", from_entry="Short", limit=tp_price)

    if use_trailing_stop
        trail_amount = close * trailing_stop_pct / 100
        strategy.exit("Short Trail", from_entry="Short", trail_points=trail_amount/syminfo.mintick)

// === 信号出场 ===
if long_exit_signal
    strategy.close("Long", comment="信号出场")

if short_exit_signal
    strategy.close("Short", comment="信号出场")

// === 趋势出场 ===
if long_trend_exit
    strategy.close("Long", comment="趋势出场")

if short_trend_exit
    strategy.close("Short", comment="趋势出场")

// ===========================
// 策略信息显示
// ===========================

// === 策略信号标记 ===
plotshape(long_condition, title="策略买入", style=shape.triangleup, location=location.belowbar,
          color=color.new(color.lime, 0), size=size.normal, text="买入")
plotshape(short_condition, title="策略卖出", style=shape.triangledown, location=location.abovebar,
          color=color.new(color.red, 0), size=size.normal, text="卖出")

// === 策略信息面板 ===
var table strategy_table = table.new(position.bottom_right, 2, 8, bgcolor=color.new(color.white, 20), border_width=2)

if barstate.islast
    // 策略基本信息
    table.cell(strategy_table, 0, 0, "策略模式", text_color=color.black, bgcolor=color.new(color.gray, 50))
    table.cell(strategy_table, 1, 0, signal_mode, text_color=color.blue)

    table.cell(strategy_table, 0, 1, "交易方向", text_color=color.black)
    table.cell(strategy_table, 1, 1, trade_direction, text_color=color.black)

    table.cell(strategy_table, 0, 2, "当前持仓", text_color=color.black)
    position_text = in_long ? "多头" : in_short ? "空头" : "无持仓"
    position_color = in_long ? color.green : in_short ? color.red : color.gray
    table.cell(strategy_table, 1, 2, position_text, text_color=position_color)

    table.cell(strategy_table, 0, 3, "持仓数量", text_color=color.black)
    table.cell(strategy_table, 1, 3, str.tostring(strategy.opentrades), text_color=color.black)

    // 信号状态
    table.cell(strategy_table, 0, 4, "UT Bot", text_color=color.black)
    ut_status = ut_buy_signal ? "买入" : ut_sell_signal ? "卖出" : "中性"
    ut_color = ut_buy_signal ? color.green : ut_sell_signal ? color.red : color.gray
    table.cell(strategy_table, 1, 4, ut_status, text_color=ut_color)

    table.cell(strategy_table, 0, 5, "SuperTrend", text_color=color.black)
    st_status = st_buy_signal ? "买入" : st_sell_signal ? "卖出" : "中性"
    st_color = st_buy_signal ? color.green : st_sell_signal ? color.red : color.gray
    table.cell(strategy_table, 1, 5, st_status, text_color=st_color)

    // 策略表现
    table.cell(strategy_table, 0, 6, "总盈亏", text_color=color.black)
    pnl_color = strategy.netprofit > 0 ? color.green : strategy.netprofit < 0 ? color.red : color.gray
    table.cell(strategy_table, 1, 6, str.tostring(strategy.netprofit, "#.##"), text_color=pnl_color)

    table.cell(strategy_table, 0, 7, "胜率", text_color=color.black)
    win_rate = strategy.closedtrades > 0 ? strategy.wintrades / strategy.closedtrades * 100 : 0
    win_color = win_rate > 50 ? color.green : color.red
    table.cell(strategy_table, 1, 7, str.tostring(win_rate, "#.#") + "%", text_color=win_color)

// === 增强警报 ===
alertcondition(long_condition, title="策略买入信号", message="双重趋势策略买入信号 - {{ticker}} at {{close}}")
alertcondition(short_condition, title="策略卖出信号", message="双重趋势策略卖出信号 - {{ticker}} at {{close}}")
alertcondition(long_exit_signal or short_exit_signal, title="策略出场信号", message="双重趋势策略出场信号 - {{ticker}} at {{close}}")

// === 背景颜色增强 ===
// 策略持仓背景
strategy_bg_color = in_long ? color.new(color.green, 95) : in_short ? color.new(color.red, 95) : na
bgcolor(strategy_bg_color, title="策略持仓背景")
