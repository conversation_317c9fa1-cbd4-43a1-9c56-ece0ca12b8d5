//@version=5
strategy("Breakout Pro+ Enhanced Strategy", overlay=true, default_qty_type=strategy.percent_of_equity, default_qty_value=10, max_bars_back=500)

// === 基础参数 ===
var g1 = "=== 突破设置 ==="
length       = input.int(20, title="Breakout Lookback", group=g1)
confirm_bars = input.int(2, title="Breakout Confirmation Bars", minval=1, maxval=5, group=g1)
min_breakout_pct = input.float(0.5, title="Minimum Breakout % (避免假突破)", minval=0.1, maxval=2.0, group=g1)

var g2 = "=== 趋势过滤 ==="
ema_fast     = input.int(50, title="Fast EMA", group=g2)
ema_slow     = input.int(200, title="Slow EMA (Trend Filter)", group=g2)
htf_tf       = input.timeframe("60", title="Higher Timeframe", group=g2)
use_htf      = input.bool(true, title="Use Higher Timeframe Trend Filter?", group=g2)

var g3 = "=== 成交量过滤 ==="
vol_len      = input.int(20, title="Volume SMA Length", group=g3)
vol_ratio    = input.float(1.5, title="Volume Spike Threshold", group=g3)
use_vol_filter = input.bool(true, title="Enable Volume Filter", group=g3)

var g4 = "=== 波动率过滤 ==="
use_atr_filter = input.bool(true, title="Enable ATR Filter", group=g4)
atr_len      = input.int(14, title="ATR Length", group=g4)
atr_mult     = input.float(1.5, title="ATR Multiplier (最小波动要求)", group=g4)

var g5 = "=== 风险管理 ==="
trailing_tp  = input.float(1.5, title="Trailing TP (% of entry)", group=g5)
fixed_tp_pct = input.float(3.0, title="Fixed Take Profit %", group=g5)
use_partial_tp = input.bool(true, title="Enable Partial Take Profit", group=g5)
partial_tp_pct = input.float(1.5, title="Partial TP % (50% position)", group=g5)
max_drawdown = input.float(5.0, title="Max Drawdown % (策略暂停)", group=g5)

var g6 = "=== 交易控制 ==="
max_trades   = input.int(1, title="Max Simultaneous Trades", minval=1, group=g6)
direction    = input.string("Both", options=["Both", "Long Only", "Short Only"], title="Trade Direction", group=g6)
trade_start_hour = input.int(9, title="Trading Start Hour", minval=0, maxval=23, group=g6)
trade_end_hour = input.int(17, title="Trading End Hour", minval=0, maxval=23, group=g6)
use_time_filter = input.bool(false, title="Enable Time Filter", group=g6)

// === 时间过滤 ===
current_hour = hour(time)
time_valid = not use_time_filter or (current_hour >= trade_start_hour and current_hour <= trade_end_hour)

// === 多周期趋势过滤 ===
ema_slow_htf = request.security(syminfo.tickerid, htf_tf, ta.ema(close, ema_slow))
ema_fast_htf = request.security(syminfo.tickerid, htf_tf, ta.ema(close, ema_fast))
htf_uptrend  = ema_fast_htf > ema_slow_htf
htf_dntrend  = ema_fast_htf < ema_slow_htf

// === 当前周期趋势过滤 ===
ema_fast_cur = ta.ema(close, ema_fast)
ema_slow_cur = ta.ema(close, ema_slow)
trend_up     = ema_fast_cur > ema_slow_cur
trend_down   = ema_fast_cur < ema_slow_cur

// === 趋势强度确认 ===
ema_distance = math.abs(ema_fast_cur - ema_slow_cur) / ema_slow_cur * 100
trend_strong = ema_distance > 0.5  // EMA距离超过0.5%才认为趋势足够强

// === 成交量过滤 ===
vol_avg  = ta.sma(volume, vol_len)
vol_valid = not use_vol_filter or volume > vol_avg * vol_ratio

// === 波动率过滤 ===
atr_val = ta.atr(atr_len)
atr_valid = not use_atr_filter or atr_val > ta.sma(atr_val, 20) * atr_mult

// === 改进的突破信号 ===
high_lv = ta.highest(high, length)
low_lv  = ta.lowest(low, length)

// 基础突破
basic_break_up = ta.crossover(close, high_lv)
basic_break_dn = ta.crossunder(close, low_lv)

// 突破幅度确认
breakout_up_pct = (close - high_lv[1]) / high_lv[1] * 100
breakout_dn_pct = (low_lv[1] - close) / low_lv[1] * 100

// 确认突破（需要足够的突破幅度）
break_up = basic_break_up and breakout_up_pct >= min_breakout_pct
break_dn = basic_break_dn and breakout_dn_pct >= min_breakout_pct

// 突破确认计数器
var int break_up_count = 0
var int break_dn_count = 0

if break_up
    break_up_count := 1
else if break_up_count > 0 and break_up_count < confirm_bars and close > high_lv
    break_up_count += 1
else
    break_up_count := 0

if break_dn
    break_dn_count := 1
else if break_dn_count > 0 and break_dn_count < confirm_bars and close < low_lv
    break_dn_count += 1
else
    break_dn_count := 0

// 确认的突破信号
confirmed_break_up = break_up_count >= confirm_bars
confirmed_break_dn = break_dn_count >= confirm_bars

// === 回撤保护 ===
var float peak_equity = 0.0
current_equity = strategy.equity
if current_equity > peak_equity
    peak_equity := current_equity

drawdown_pct = (peak_equity - current_equity) / peak_equity * 100
drawdown_protection = drawdown_pct < max_drawdown

// === 仓位检测 ===
in_long  = strategy.position_size > 0
in_short = strategy.position_size < 0

// === 多空方向控制 ===
allow_long  = (direction == "Both" or direction == "Long Only")
allow_short = (direction == "Both" or direction == "Short Only")

// === 改进的入场条件 ===
base_long_cond = confirmed_break_up and trend_up and trend_strong and vol_valid and atr_valid and time_valid and drawdown_protection
base_short_cond = confirmed_break_dn and trend_down and trend_strong and vol_valid and atr_valid and time_valid and drawdown_protection

// 多头信号
long_cond = base_long_cond and allow_long and (not in_long) and (strategy.opentrades < max_trades)
if use_htf
    long_cond := long_cond and htf_uptrend

// 空头信号
short_cond = base_short_cond and allow_short and (not in_short) and (strategy.opentrades < max_trades)
if use_htf
    short_cond := short_cond and htf_dntrend

// === 动态止损计算 ===
dynamic_stop_long = math.min(low[length], close - atr_val * 2)
dynamic_stop_short = math.max(high[length], close + atr_val * 2)

// === 入场执行 ===
if (long_cond)
    strategy.entry("Long", strategy.long, comment="Enhanced Long")

    // 动态止损
    stop_price = dynamic_stop_long

    // 固定止盈
    tp_price = close * (1 + fixed_tp_pct / 100)

    if use_partial_tp
        // 部分止盈 (50%仓位)
        partial_tp_price = close * (1 + partial_tp_pct / 100)
        strategy.exit("Partial TP", from_entry="Long", qty_percent=50, limit=partial_tp_price)

        // 剩余仓位追踪止盈
        trail_amount = close * trailing_tp / 100
        strategy.exit("Trail Exit", from_entry="Long", qty_percent=50, stop=stop_price, trail_points=trail_amount/syminfo.mintick)
    else
        // 传统止盈止损
        trail_amount = close * trailing_tp / 100
        strategy.exit("Exit Long", from_entry="Long", stop=stop_price, limit=tp_price, trail_points=trail_amount/syminfo.mintick)

if (short_cond)
    strategy.entry("Short", strategy.short, comment="Enhanced Short")

    // 动态止损
    stop_price = dynamic_stop_short

    // 固定止盈
    tp_price = close * (1 - fixed_tp_pct / 100)

    if use_partial_tp
        // 部分止盈 (50%仓位)
        partial_tp_price = close * (1 - partial_tp_pct / 100)
        strategy.exit("Partial TP", from_entry="Short", qty_percent=50, limit=partial_tp_price)

        // 剩余仓位追踪止盈
        trail_amount = close * trailing_tp / 100
        strategy.exit("Trail Exit", from_entry="Short", qty_percent=50, stop=stop_price, trail_points=trail_amount/syminfo.mintick)
    else
        // 传统止盈止损
        trail_amount = close * trailing_tp / 100
        strategy.exit("Exit Short", from_entry="Short", stop=stop_price, limit=tp_price, trail_points=trail_amount/syminfo.mintick)

// === 信号标记 ===
plotshape(long_cond, title="Long Signal", location=location.belowbar, style=shape.triangleup, size=size.normal, color=color.green)
plotshape(short_cond, title="Short Signal", location=location.abovebar, style=shape.triangledown, size=size.normal, color=color.red)

// === 可视化辅助 ===
plot(high_lv, color=color.new(color.green, 50), title="Breakout High", linewidth=2)
plot(low_lv, color=color.new(color.red, 50), title="Breakout Low", linewidth=2)
plot(ema_fast_cur, color=color.orange, title="EMA Fast", linewidth=2)
plot(ema_slow_cur, color=color.blue, title="EMA Slow", linewidth=2)

// 动态止损线
plot(in_long ? dynamic_stop_long : na, color=color.red, style=plot.style_stepline, title="Long Stop")
plot(in_short ? dynamic_stop_short : na, color=color.red, style=plot.style_stepline, title="Short Stop")

// === 信息面板 ===
var table info_table = table.new(position.top_right, 2, 8, bgcolor=color.white, border_width=1)

if barstate.islast
    table.cell(info_table, 0, 0, "策略状态", text_color=color.black, bgcolor=color.gray)
    table.cell(info_table, 1, 0, drawdown_protection ? "正常" : "暂停", text_color=drawdown_protection ? color.green : color.red)

    table.cell(info_table, 0, 1, "当前回撤", text_color=color.black)
    table.cell(info_table, 1, 1, str.tostring(drawdown_pct, "#.##") + "%", text_color=color.black)

    table.cell(info_table, 0, 2, "趋势强度", text_color=color.black)
    table.cell(info_table, 1, 2, str.tostring(ema_distance, "#.##") + "%", text_color=trend_strong ? color.green : color.red)

    table.cell(info_table, 0, 3, "成交量状态", text_color=color.black)
    table.cell(info_table, 1, 3, vol_valid ? "放大" : "正常", text_color=vol_valid ? color.green : color.gray)

    table.cell(info_table, 0, 4, "波动率状态", text_color=color.black)
    table.cell(info_table, 1, 4, atr_valid ? "活跃" : "平静", text_color=atr_valid ? color.green : color.gray)

    table.cell(info_table, 0, 5, "时间过滤", text_color=color.black)
    table.cell(info_table, 1, 5, time_valid ? "开放" : "关闭", text_color=time_valid ? color.green : color.red)

    table.cell(info_table, 0, 6, "持仓数量", text_color=color.black)
    table.cell(info_table, 1, 6, str.tostring(strategy.opentrades), text_color=color.black)

    table.cell(info_table, 0, 7, "总盈亏", text_color=color.black)
    pnl_color = strategy.netprofit > 0 ? color.green : strategy.netprofit < 0 ? color.red : color.gray
    table.cell(info_table, 1, 7, str.tostring(strategy.netprofit, "#.##"), text_color=pnl_color)

// === 警报设置 ===
alertcondition(long_cond, title="Long Entry Alert", message="Enhanced Breakout Long Signal - {{ticker}} at {{close}}")
alertcondition(short_cond, title="Short Entry Alert", message="Enhanced Breakout Short Signal - {{ticker}} at {{close}}")
alertcondition(ta.crossunder(close, dynamic_stop_long) and in_long, title="Long Stop Alert", message="Long Stop Loss Hit - {{ticker}} at {{close}}")
alertcondition(ta.crossover(close, dynamic_stop_short) and in_short, title="Short Stop Alert", message="Short Stop Loss Hit - {{ticker}} at {{close}}")
