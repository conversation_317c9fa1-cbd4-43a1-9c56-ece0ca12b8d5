//@version=6
indicator("最近两个高点连线", shorttitle="高点连线", overlay=true, max_lines_count=10)

// ===========================
// 参数设置
// ===========================

pivot_length = input.int(5, title="枢轴长度", minval=1, maxval=20, tooltip="用于识别高点的左右K线数量")
line_color = input.color(color.red, title="连线颜色")
line_width = input.int(2, title="连线宽度", minval=1, maxval=5)
line_style = input.string("实线", title="连线样式", options=["实线", "虚线", "点线"])
extend_line = input.bool(true, title="延伸连线", tooltip="向右延伸连线")
show_labels = input.bool(true, title="显示高点标签")

// ===========================
// 高点识别
// ===========================

// 识别枢轴高点
pivot_high = ta.pivothigh(high, pivot_length, pivot_length)

// ===========================
// 存储最近两个高点
// ===========================

// 高点数据结构
type HighPoint
    int bar_index
    float price

// 存储最近的两个高点
var HighPoint first_high = na
var HighPoint second_high = na

// 存储连线对象
var line current_line = na

// ===========================
// 检测新高点并更新连线
// ===========================

if not na(pivot_high)
    // 创建新的高点
    new_high = HighPoint.new(bar_index[pivot_length], pivot_high)

    // 更新高点序列
    second_high := first_high
    first_high := new_high

    // 删除旧连线
    if not na(current_line)
        line.delete(current_line)
        current_line := na

    // 如果有两个高点，创建连线
    if not na(second_high)
        // 确定连线样式
        style = switch line_style
            "虚线" => line.style_dashed
            "点线" => line.style_dotted
            => line.style_solid

        // 确定延伸方式
        extend_type = extend_line ? extend.right : extend.none

        // 创建连线
        current_line := line.new(x1=second_high.bar_index, y1=second_high.price, x2=first_high.bar_index, y2=first_high.price, color=line_color, width=line_width, style=style, extend=extend_type)

    // 添加高点标签
    if show_labels
        label_text = "H: " + str.tostring(pivot_high, "#.##")
        label.new(new_high.bar_index, pivot_high, text=label_text, style=label.style_label_down, color=line_color, textcolor=color.white, size=size.small)

// ===========================
// 信息面板
// ===========================

var table info_table = table.new(position.top_right, 2, 4, bgcolor=color.new(color.white, 20), border_width=1)

if barstate.islast
    table.clear(info_table, 0, 0, 1, 3)

    table.cell(info_table, 0, 0, "最近两个高点连线", text_color=color.black, bgcolor=color.new(color.gray, 50), text_size=size.small)
    table.cell(info_table, 1, 0, "", text_color=color.black, bgcolor=color.new(color.gray, 50))

    if not na(first_high)
        table.cell(info_table, 0, 1, "最新高点", text_color=color.black, text_size=size.small)
        table.cell(info_table, 1, 1, str.tostring(first_high.price, "#.##"), text_color=color.red, text_size=size.small)

    if not na(second_high)
        table.cell(info_table, 0, 2, "前一高点", text_color=color.black, text_size=size.small)
        table.cell(info_table, 1, 2, str.tostring(second_high.price, "#.##"), text_color=color.red, text_size=size.small)

        // 计算斜率和趋势
        if first_high.bar_index != second_high.bar_index
            slope = (first_high.price - second_high.price) / (first_high.bar_index - second_high.bar_index)
            trend_text = slope > 0 ? "上升" : slope < 0 ? "下降" : "平行"
            trend_color = slope > 0 ? color.green : slope < 0 ? color.red : color.gray
            table.cell(info_table, 0, 3, "趋势方向", text_color=color.black, text_size=size.small)
            table.cell(info_table, 1, 3, trend_text, text_color=trend_color, text_size=size.small)

// ===========================
// 警报
// ===========================

alertcondition(not na(pivot_high), title="新高点检测", message="检测到新的高点，连线已更新")
alertcondition(not na(pivot_high) and not na(second_high), title="高点连线更新", message="最近两个高点的连线已更新")