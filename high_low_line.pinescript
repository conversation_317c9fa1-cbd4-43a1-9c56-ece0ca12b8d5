//@version=6
indicator("最近四个高点连线", shorttitle="高点连线", overlay=true, max_lines_count=10)

// ===========================
// 参数设置
// ===========================

pivot_length = input.int(5, title="枢轴长度", minval=1, maxval=20, tooltip="用于识别高点的左右K线数量")
line_color = input.color(color.red, title="连线颜色")
line_width = input.int(2, title="连线宽度", minval=1, maxval=5)
line_style = input.string("实线", title="连线样式", options=["实线", "虚线", "点线"])
extend_line = input.bool(true, title="延伸连线", tooltip="向右延伸连线")
show_labels = input.bool(true, title="显示高点标签")

// ===========================
// 高点识别
// ===========================

// 识别枢轴高点
pivot_high = ta.pivothigh(high, pivot_length, pivot_length)

// ===========================
// 存储最近两个高点
// ===========================

// 高点数据结构
type HighPoint
    int bar_index
    float price

// 存储最近的四个高点
var HighPoint high1 = na  // 最新高点
var HighPoint high2 = na  // 第二新高点
var HighPoint high3 = na  // 第三新高点
var HighPoint high4 = na  // 第四新高点

// 存储连线对象
var line line1 = na  // high1 到 high2 的连线
var line line2 = na  // high2 到 high3 的连线
var line line3 = na  // high3 到 high4 的连线

// ===========================
// 检测新高点并更新连线
// ===========================

if not na(pivot_high)
    // 获取实际高点的K线索引和价格
    high_bar_index = bar_index - pivot_length
    high_price = high[pivot_length]  // 获取实际K线的最高价

    // 创建新的高点（使用K线的实际最高价）
    new_high = HighPoint.new(high_bar_index, high_price)

    // 更新高点序列（向后推移）
    high4 := high3
    high3 := high2
    high2 := high1
    high1 := new_high

    // 删除所有旧连线
    if not na(line1)
        line.delete(line1)
        line1 := na
    if not na(line2)
        line.delete(line2)
        line2 := na
    if not na(line3)
        line.delete(line3)
        line3 := na

    // 确定连线样式
    style = switch line_style
        "虚线" => line.style_dashed
        "点线" => line.style_dotted
        => line.style_solid

    // 确定延伸方式（只有最新连线延伸）
    extend_type = extend_line ? extend.right : extend.none

    // 创建连线：连接相邻的高点
    if not na(high2)
        // 最新连线：high1 到 high2（可延伸）
        line1 := line.new(x1=high2.bar_index, y1=high2.price, x2=high1.bar_index, y2=high1.price, color=line_color, width=line_width, style=style, extend=extend_type)

    if not na(high3)
        // 第二条连线：high2 到 high3（不延伸）
        line2 := line.new(x1=high3.bar_index, y1=high3.price, x2=high2.bar_index, y2=high2.price, color=color.new(line_color, 30), width=line_width-1, style=line.style_solid, extend=extend.none)

    if not na(high4)
        // 第三条连线：high3 到 high4（不延伸）
        line3 := line.new(x1=high4.bar_index, y1=high4.price, x2=high3.bar_index, y2=high3.price, color=color.new(line_color, 50), width=line_width-1, style=line.style_solid, extend=extend.none)

    // 添加高点标签（标签位置与连线无关）
    if show_labels
        label_text = "H" + str.tostring(1) + ": " + str.tostring(high_price, "#.##")
        label.new(high_bar_index, high_price, text=label_text, style=label.style_label_down, color=line_color, textcolor=color.white, size=size.small)

// ===========================
// 信息面板
// ===========================

var table info_table = table.new(position.top_right, 2, 6, bgcolor=color.new(color.white, 20), border_width=1)

if barstate.islast
    table.clear(info_table, 0, 0, 1, 5)

    table.cell(info_table, 0, 0, "最近四个高点连线", text_color=color.black, bgcolor=color.new(color.gray, 50), text_size=size.small)
    table.cell(info_table, 1, 0, "", text_color=color.black, bgcolor=color.new(color.gray, 50))

    // 显示四个高点
    if not na(high1)
        table.cell(info_table, 0, 1, "最新高点", text_color=color.black, text_size=size.small)
        table.cell(info_table, 1, 1, str.tostring(high1.price, "#.##"), text_color=color.red, text_size=size.small)

    if not na(high2)
        table.cell(info_table, 0, 2, "第二高点", text_color=color.black, text_size=size.small)
        table.cell(info_table, 1, 2, str.tostring(high2.price, "#.##"), text_color=color.new(color.red, 30), text_size=size.small)

    if not na(high3)
        table.cell(info_table, 0, 3, "第三高点", text_color=color.black, text_size=size.small)
        table.cell(info_table, 1, 3, str.tostring(high3.price, "#.##"), text_color=color.new(color.red, 50), text_size=size.small)

    if not na(high4)
        table.cell(info_table, 0, 4, "第四高点", text_color=color.black, text_size=size.small)
        table.cell(info_table, 1, 4, str.tostring(high4.price, "#.##"), text_color=color.new(color.red, 70), text_size=size.small)

    // 计算最新连线的趋势
    if not na(high1) and not na(high2)
        if high1.bar_index != high2.bar_index
            slope = (high1.price - high2.price) / (high1.bar_index - high2.bar_index)
            trend_text = slope > 0 ? "上升" : slope < 0 ? "下降" : "平行"
            trend_color = slope > 0 ? color.green : slope < 0 ? color.red : color.gray
            table.cell(info_table, 0, 5, "最新趋势", text_color=color.black, text_size=size.small)
            table.cell(info_table, 1, 5, trend_text, text_color=trend_color, text_size=size.small)

// ===========================
// 警报
// ===========================

alertcondition(not na(pivot_high), title="新高点检测", message="检测到新的高点，连线已更新")
alertcondition(not na(pivot_high) and not na(second_high), title="高点连线更新", message="最近两个高点的连线已更新")