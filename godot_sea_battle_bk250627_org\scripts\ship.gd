extends RefCounted
class_name Ship
## 船只类
##
## 管理船只状态和位置
## @version: 4.4.1

# 船只大小
var size: int
# 船只位置 (左上角坐标)
var x: int
var y: int
# 船只方向 (true = 水平, false = 垂直)
var horizontal: bool
# 船只的所有格子位置
var positions: Array[Vector2i] = []
# 被击中的位置
var hits: Array[Vector2i] = []

# 构造函数
func _init(ship_size: int, start_x: int, start_y: int, is_horizontal: bool):
	size = ship_size
	x = start_x
	y = start_y
	horizontal = is_horizontal
	
	# 计算船只占据的所有格子
	positions = []
	for i in range(size):
		if horizontal:
			positions.append(Vector2i(x + i, y))
		else:
			positions.append(Vector2i(x, y + i))

# 检查船只是否在棋盘范围内
func is_within_bounds(board_size: int) -> bool:
	for pos in positions:
		if pos.x < 0 or pos.x >= board_size or pos.y < 0 or pos.y >= board_size:
			return false
	return true

# 检查是否与另一艘船重叠
func overlaps_with(other_ship: Ship) -> bool:
	for pos in positions:
		for other_pos in other_ship.positions:
			if pos == other_pos:
				return true
	return false

# 检查是否与任何船重叠
func overlaps_with_any(ships: Array) -> bool:
	for ship in ships:
		if overlaps_with(ship):
			return true
	return false

# 检查是否包含指定位置
func contains_position(pos_x: int, pos_y: int) -> bool:
	var pos = Vector2i(pos_x, pos_y)
	return positions.has(pos)

# 检查是否在指定位置
func is_at_position(pos_x: int, pos_y: int) -> bool:
	return contains_position(pos_x, pos_y)

# 接收攻击
func hit() -> void:
	# 增加命中次数，使用占位坐标(-1, -1)
	hits.append(Vector2i(-1, -1))

# 检查是否被击沉
func is_sunk() -> bool:
	return hits.size() == size
