[res://scripts/ui_controller.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 17,
"scroll_position": 11.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/board.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 6,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 14,
"scroll_position": 2.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/game.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 23,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 143,
"scroll_position": 132.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/ship.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 0,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 6,
"scroll_position": 0.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/board_display.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 2,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 160,
"scroll_position": 153.0,
"selection": false,
"syntax_highlighter": "GDScript"
}

[res://scripts/game_manager.gd]

state={
"bookmarks": PackedInt32Array(),
"breakpoints": PackedInt32Array(),
"column": 23,
"folded_lines": Array[int]([]),
"h_scroll_position": 0,
"row": 6,
"scroll_position": 1.0,
"selection": false,
"syntax_highlighter": "GDScript"
}
