extends RefCounted
class_name AI

const Board = preload("res://scripts/board.gd")
## 游戏AI控制器
##
## 实现不同难度级别的AI逻辑
## @version: 4.4.1

# AI难度级别
enum Difficulty {
    EASY = 0,
    MEDIUM = 1,
    HARD = 2
}

# 当前难度
var difficulty: int = Difficulty.MEDIUM
# 对手的棋盘
var opponent_board: Board
# 已经攻击过的位置
var attacked_positions: Array[Vector2i] = []
# 当前的目标船位置
var target_positions: Array[Vector2i] = []
# 上一次命中的位置
var last_hit_position: Vector2i = Vector2i(-1, -1)
# 当前攻击方向
var current_direction: Vector2i = Vector2i.ZERO
# 可能的方向
const DIRECTIONS = [Vector2i.RIGHT, Vector2i.DOWN, Vector2i.LEFT, Vector2i.UP]

# 初始化
func _init(board: Board, diff: int = Difficulty.MEDIUM):
    opponent_board = board
    difficulty = diff
    reset()

# 重置 AI 状态
func reset() -> void:
    attacked_positions.clear()
    target_positions.clear()
    last_hit_position = Vector2i(-1, -1)
    current_direction = Vector2i.ZERO

# 获取 AI 的下一步攻击位置
func get_next_attack() -> Vector2i:
    match difficulty:
        Difficulty.EASY:
            return get_random_attack()
        Difficulty.MEDIUM:
            return get_medium_attack()
        Difficulty.HARD:
            return get_hard_attack()
        _:
            return get_random_attack()

# 简单难度：完全随机攻击
func get_random_attack() -> Vector2i:
    var available_positions: Array[Vector2i] = []
    
    # 收集所有未攻击过的位置
    for y in range(opponent_board.BOARD_SIZE):
        for x in range(opponent_board.BOARD_SIZE):
            var pos = Vector2i(x, y)
            if not attacked_positions.has(pos):
                available_positions.append(pos)
    
    # 如果没有可用位置，返回无效位置
    if available_positions.is_empty():
        return Vector2i(-1, -1)
    
    # 随机选择一个位置
    var index = randi() % available_positions.size()
    var attack_pos = available_positions[index]
    
    # 记录这个位置已被攻击
    attacked_positions.append(attack_pos)
    
    return attack_pos

# 中等难度：如果击中船，则尝试周围位置
func get_medium_attack() -> Vector2i:
    # 如果有目标位置，优先攻击
    if not target_positions.is_empty():
        var attack_pos = target_positions.pop_front()
        
        # 如果这个位置已经被攻击过，递归调用获取下一个位置
        if attacked_positions.has(attack_pos):
            return get_medium_attack()
        
        # 记录这个位置已被攻击
        attacked_positions.append(attack_pos)
        
        # 如果击中了船
        if opponent_board.has_ship_at(attack_pos.x, attack_pos.y):
            # 添加周围的位置作为目标
            add_adjacent_positions(attack_pos)
            last_hit_position = attack_pos
        
        return attack_pos
    
    # 否则随机攻击
    return get_random_attack()

# 困难难度：使用更智能的策略
func get_hard_attack() -> Vector2i:
    # 如果有目标位置，优先攻击
    if not target_positions.is_empty():
        var attack_pos = target_positions.pop_front()
        
        # 如果这个位置已经被攻击过，递归调用获取下一个位置
        if attacked_positions.has(attack_pos):
            return get_hard_attack()
        
        # 记录这个位置已被攻击
        attacked_positions.append(attack_pos)
        
        # 如果击中了船
        if opponent_board.has_ship_at(attack_pos.x, attack_pos.y):
            # 如果上一次也击中了船，确定攻击方向
            if last_hit_position.x >= 0 and last_hit_position.y >= 0:
                current_direction = attack_pos - last_hit_position
                
                # 添加下一个方向的位置
                var next_pos = attack_pos + current_direction
                if is_valid_position(next_pos) and not attacked_positions.has(next_pos):
                    target_positions.push_front(next_pos)
                
                # 如果当前方向不可行，尝试反方向
                if target_positions.is_empty():
                    next_pos = last_hit_position - current_direction
                    while is_valid_position(next_pos) and not attacked_positions.has(next_pos):
                        target_positions.push_back(next_pos)
                        next_pos -= current_direction
            else:
                # 第一次击中，添加周围的位置作为目标
                add_adjacent_positions(attack_pos)
            
            last_hit_position = attack_pos
        else:
            # 如果未击中，且有上一次击中的位置，尝试其他方向
            if last_hit_position.x >= 0 and last_hit_position.y >= 0 and target_positions.is_empty():
                for dir in DIRECTIONS:
                    if dir != current_direction and dir != -current_direction:
                        var next_pos = last_hit_position + dir
                        if is_valid_position(next_pos) and not attacked_positions.has(next_pos):
                            target_positions.push_back(next_pos)
        
        return attack_pos
    
    # 使用棋盘模式攻击
    return get_patterned_attack()

# 添加相邻位置到目标列表
func add_adjacent_positions(pos: Vector2i) -> void:
    for dir in DIRECTIONS:
        var adjacent_pos = pos + dir
        if is_valid_position(adjacent_pos) and not attacked_positions.has(adjacent_pos):
            target_positions.push_back(adjacent_pos)

# 检查位置是否有效
func is_valid_position(pos: Vector2i) -> bool:
    return pos.x >= 0 and pos.x < opponent_board.BOARD_SIZE and pos.y >= 0 and pos.y < opponent_board.BOARD_SIZE

# 使用棋盘模式攻击（隔行隔列）
func get_patterned_attack() -> Vector2i:
    var available_positions: Array[Vector2i] = []
    
    # 收集所有未攻击过的位置，使用棋盘模式
    for y in range(opponent_board.BOARD_SIZE):
        for x in range(opponent_board.BOARD_SIZE):
            if (x + y) % 2 == 0:  # 棋盘模式
                var pos = Vector2i(x, y)
                if not attacked_positions.has(pos):
                    available_positions.append(pos)
    
    # 如果没有可用的棋盘模式位置，使用任何未攻击的位置
    if available_positions.is_empty():
        for y in range(opponent_board.BOARD_SIZE):
            for x in range(opponent_board.BOARD_SIZE):
                var pos = Vector2i(x, y)
                if not attacked_positions.has(pos):
                    available_positions.append(pos)
    
    # 如果没有可用位置，返回无效位置
    if available_positions.is_empty():
        return Vector2i(-1, -1)
    
    # 随机选择一个位置
    var index = randi() % available_positions.size()
    var attack_pos = available_positions[index]
    
    # 记录这个位置已被攻击
    attacked_positions.append(attack_pos)
    
    return attack_pos

# 注册攻击结果
func register_attack_result(position: Vector2i, result: Dictionary) -> void:
    # 可以在这里添加更高级的AI逻辑，根据攻击结果调整策略
    pass