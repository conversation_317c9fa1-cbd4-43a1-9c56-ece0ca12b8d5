extends Control
class_name UIController

const Board = preload("res://scripts/board.gd")
## 用户界面控制器
##
## 管理游戏UI和用户交互
## @version: 4.4.1

# 当前选中的船只大小
var selected_ship_size: int = 0
# 当前船只方向
var is_horizontal: bool = true
# 是否在放置船只阶段
var is_placing_ships: bool = true

# 节点引用
@onready var player_board_node: BoardDisplay = $PlayerBoard
@onready var ai_board_node: BoardDisplay = $AIBoard
@onready var ship_buttons: Array[Node] = $Controls/ShipButtons.get_children().filter(func(node): return node is Button and node.has_meta("ship_size"))
@onready var rotate_button: Button = $Controls/ShipButtons/RotateButton
@onready var auto_place_button: Button = $Controls/ShipButtons/AutoPlaceButton
@onready var start_button: Button = $Controls/ShipButtons/StartButton
@onready var restart_button: Button = $Controls/ShipButtons/RestartButton
@onready var state_label: Label = $Controls/StatusLabel
@onready var message_label: Label = $Controls/MessageLabel
@onready var game:Game = $"../Game"

# 初始化
func _ready():
	# 创建游戏实例
	print_debug("ready")

	# 设置棋盘类型标识
	player_board_node.is_player_board = true
	ai_board_node.is_player_board = false

	# 连接游戏信号
	game.game_started.connect(_on_game_started)
	game.game_reset.connect(_on_game_reset)
	game.state_changed.connect(_on_state_changed)
	game.game_ended.connect(_on_game_ended)
	game.attack_result.connect(_on_attack_result)
	game.ship_sunk.connect(_on_ship_sunk)

	# 连接按钮信号
	for button in ship_buttons:
		button.pressed.connect(_on_ship_button_pressed.bind(button.get_meta("ship_size")))

	rotate_button.pressed.connect(_on_rotate_button_pressed)
	auto_place_button.pressed.connect(_on_auto_place_button_pressed)
	start_button.pressed.connect(_on_start_button_pressed)
	restart_button.pressed.connect(_on_restart_button_pressed)

	# 连接棋盘信号
	player_board_node.cell_clicked.connect(_on_player_board_cell_clicked)
	ai_board_node.cell_clicked.connect(_on_ai_board_cell_clicked)

	# 初始化UI
	update_ui()

# 检查是否可以放置指定大小的船
func can_place_ship(size: int) -> bool:
	var remaining = game.get_player_board().get_remaining_ships()
	return remaining.get(size, 0) > 0

# 更新UI状态
func update_ui() -> void:
	# 更新状态标签
	state_label.text = game.get_state_text()
	
	# 更新按钮状态
	for button in ship_buttons:
		var ship_size = button.get_meta("ship_size")
		button.disabled = not is_placing_ships or not can_place_ship(ship_size)
	
	rotate_button.disabled = not is_placing_ships or selected_ship_size == 0
	auto_place_button.disabled = not is_placing_ships
	start_button.visible = is_placing_ships and game.get_player_board().all_ships_placed
	restart_button.visible = not is_placing_ships  # 游戏开始后显示重新开始按键
	
	# 更新消息标签
	if is_placing_ships:
		if selected_ship_size > 0:
			message_label.text = "点击棋盘放置%d格船，按R键或点击旋转按钮旋转" % selected_ship_size
		else:
			message_label.text = "请选择要放置的船只"
	elif game.is_game_over():
		if game._get_winner() == &"player":
			message_label.text = "恭喜你获胜！"
		else:
			message_label.text = "AI获胜了，再接再厉！"
	elif game.get_current_state() == Game.GameState.PLAYER_TURN:
		message_label.text = "点击右侧棋盘攻击AI的船只（击中后可继续攻击）"
	elif game.get_current_state() == Game.GameState.AI_TURN:
		message_label.text = "AI正在思考..."
	
	# 更新棋盘显示
	update_board_displays()

# 更新棋盘显示
func update_board_displays() -> void:
	# 更新玩家棋盘显示（始终显示船只）
	player_board_node.update_display(game.get_player_board(), true)

	# 更新AI棋盘显示（游戏开始后不显示AI的船只）
	var show_ai_ships = is_placing_ships  # 只在放置阶段显示AI船只（用于调试）
	ai_board_node.update_display(game.get_ai_board(), show_ai_ships)

# 船只按钮点击
func _on_ship_button_pressed(ship_size: int) -> void:
	selected_ship_size = ship_size if selected_ship_size != ship_size else 0
	update_ui()

# 旋转按钮点击
func _on_rotate_button_pressed() -> void:
	is_horizontal = !is_horizontal
	update_ui()

# 自动放置按钮点击
func _on_auto_place_button_pressed() -> void:
	if game.player_auto_place_ships():
		is_placing_ships = false
		game.start_game()
		update_ui()

# 开始按钮点击
func _on_start_button_pressed() -> void:
	is_placing_ships = false
	game.start_game()
	update_ui()

# 重新开始按钮点击
func _on_restart_button_pressed() -> void:
	print("重新开始游戏")
	# 重置游戏状态
	game.reset_game()
	is_placing_ships = true
	selected_ship_size = 0
	update_ui()

# 玩家棋盘单元格点击
func _on_player_board_cell_clicked(cell: Vector2i) -> void:
	if is_placing_ships and selected_ship_size > 0:
		print("尝试放置船只：大小=", selected_ship_size, " 位置=(", cell.x, ",", cell.y, ") 水平=", is_horizontal)
		if game.player_place_ship(selected_ship_size, cell.x, cell.y, is_horizontal):
			print("船只放置成功")
			selected_ship_size = 0
			update_ui()
		else:
			print("船只放置失败")

# AI棋盘单元格点击
func _on_ai_board_cell_clicked(cell: Vector2i) -> void:
	if not is_placing_ships and game.get_current_state() == Game.GameState.PLAYER_TURN:
		var result = game.player_attack(cell)
		if not result.is_empty():
			if result.hit:
				if result.sunk:
					print("击沉了AI的船只！")
				else:
					print("击中了AI的船只！可以继续攻击")
			else:
				print("未击中，轮到AI攻击")
			update_ui()
		else:
			print("无效攻击位置")

# 结果弹窗确认
func _on_result_popup_confirmed() -> void:
	game.reset_game()
	is_placing_ships = true
	selected_ship_size = 0
	update_ui()

# 游戏开始事件处理
func _on_game_started(_first_player: StringName) -> void:
	is_placing_ships = false
	update_ui()

# 游戏重置事件处理
func _on_game_reset() -> void:
	is_placing_ships = true
	selected_ship_size = 0
	update_ui()

# 状态变更事件处理
func _on_state_changed(_new_state: int) -> void:
	update_ui()

# 游戏结束事件处理
func _on_game_ended(_winner: StringName, _reason: StringName) -> void:
	update_ui()

# 攻击结果事件处理
func _on_attack_result(_attacker: StringName, attack_position: Vector2i, _result: Dictionary) -> void:
	update_ui()

# 船只沉没事件处理
func _on_ship_sunk(ship_owner: StringName, _ship_type: StringName) -> void:
	update_ui()
