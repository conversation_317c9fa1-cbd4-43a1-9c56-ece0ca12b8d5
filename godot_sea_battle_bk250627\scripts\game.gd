extends Node
class_name Game

const Board = preload("res://scripts/board.gd")
const AI = preload("res://scripts/ai.gd")
const BoardDisplay = preload("res://scripts/board_display.gd")
## 海战游戏主控制器
##
## 管理游戏核心逻辑、状态转换和回合制流程，提供完整的事件信号系统
##
## @tutorial: 使用示例:
##     var game = Game.new()
##     game.ai_difficulty = AI.Difficulty.HARD
##     game.game_started.connect(_on_game_started)
##     game.attack_result.connect(_on_attack_result)
##     add_child(game)
##
## @note: 游戏状态变更时会自动触发相应信号
## @note: 所有坐标参数都使用Vector2i类型
##
## @version: 4.5.0
## @copyright: (c) 2024 Your Name

## 游戏开始信号
## @param first_player: 先手玩家("player"/"ai")
signal game_started(first_player: StringName)

## 游戏重置信号
signal game_reset

## 游戏暂停状态变更信号
## @param paused: 是否暂停
# signal game_paused(paused: bool) # 预留功能

## 游戏状态变更信号
## @param new_state: 新的游戏状态(GameState枚举)
signal state_changed(new_state: int)

## 游戏结束信号
## @param winner: 获胜者("player"/"ai"/"draw")
## @param reason: 结束原因("all_ships_sunk"/"timeout"/"surrender")
signal game_ended(winner: StringName, reason: StringName)

## 攻击结果信号(玩家或AI攻击)
## @param attacker: 攻击者("player"/"ai") 
## @param position: 攻击位置(Vector2i)
## @param result: 攻击结果{hit: bool, sunk: bool, ship_type: StringName}
signal attack_result(attacker: StringName, position: Vector2i, result: Dictionary)

## 船只沉没信号
## @param owner: 船只所有者("player"/"ai")
## @param ship_type: 船只类型(StringName)
signal ship_sunk(owner: StringName, ship_type: StringName)

## 回合开始信号
## @param turn_owner: 回合所有者("player"/"ai")
signal turn_started(turn_owner: StringName)

# 游戏状态
enum GameState {
	SETUP = 0,
	PLAYER_TURN = 1,
	AI_TURN = 2, 
	GAME_OVER = 3
}

@export var ai_difficulty: int = AI.Difficulty.MEDIUM

# 游戏核心数据
var current_state: int = GameState.SETUP:
	set(value):
		if current_state != value and _can_transition_to(value):
			_exit_state(current_state)
			current_state = value
			_enter_state(value)
			state_changed.emit(value)
			print("状态变更: ", GameState.keys()[value])

# 状态转换验证
func _can_transition_to(new_state: int) -> bool:
	match [current_state, new_state]:
		[GameState.SETUP, GameState.PLAYER_TURN]:
			return player_board.all_ships_placed
		[GameState.PLAYER_TURN, GameState.AI_TURN]:
			return true
		[GameState.AI_TURN, GameState.PLAYER_TURN]:
			return true
		[_, GameState.GAME_OVER]:
			return true
		_:
			return false

# 进入状态处理
func _enter_state(state: int) -> void:
	match state:
		GameState.PLAYER_TURN:
			_turn_timer.start(TURN_TIMEOUT)
			turn_started.emit(&"player")
		GameState.AI_TURN:
			_turn_timer.stop()
			turn_started.emit(&"ai")
			# 在下一帧执行AI回合，避免信号连锁反应
			call_deferred("ai_turn")
		GameState.GAME_OVER:
			_turn_timer.stop()
			game_ended.emit(_get_winner(), _get_end_reason())

# 退出状态处理
func _exit_state(_state: int) -> void:
	pass  # 可添加状态退出时的清理逻辑

func _on_turn_timeout() -> void:
	if current_state == GameState.PLAYER_TURN:
		game_result = &"回合超时 - AI获胜!"
		current_state = GameState.GAME_OVER

var player_board: Board
var ai_board: Board
var ai_opponent: AI
var game_result: StringName = &""
var _turn_timer: Timer

const TURN_TIMEOUT := 120.0

# 初始化
func _ready():
	print_debug("ready")
	# 创建回合计时器
	_turn_timer = Timer.new()
	_turn_timer.one_shot = true
	_turn_timer.timeout.connect(_on_turn_timeout)
	add_child(_turn_timer)
	
	# 初始化游戏
	reset_game()

# 构造函数
func _init(difficulty: int = AI.Difficulty.MEDIUM):
	randomize()  # 初始化随机种子
	ai_difficulty = difficulty

# 重置游戏
func reset_game():
	player_board = Board.new()
	ai_board = Board.new()
	ai_opponent = AI.new(player_board, ai_difficulty)
	current_state = GameState.SETUP
	game_result = &""
	game_reset.emit()

# 设置游戏(与Python项目一致)
func setup_game():
	reset_game()

# 开始游戏
func start_game():
	# AI自动放置船只
	if not ai_board.auto_place_ships():
		print("AI failed to place ships!")
		return
	
	# 输出初始盘面
	print("\n=== 游戏开始 - 初始盘面 ===")
	
	# 调试信息 - 打印玩家船只位置
	print("玩家船只位置:")
	for ship in player_board.ships:
		print("大小:%d 位置:(%d,%d) 方向:%s" % [ship.size, ship.x, ship.y, "水平" if ship.horizontal else "垂直"])
	
	print("\n玩家盘面:")
	player_board.print_board_state()
	
	# 调试信息 - 打印AI船只位置
	print("\nAI船只位置:")
	for ship in ai_board.ships:
		print("大小:%d 位置:(%d,%d) 方向:%s" % [ship.size, ship.x, ship.y, "水平" if ship.horizontal else "垂直"])
	
	print("\nAI盘面:")
	ai_board.print_board_state()
	
	# 检查AI和玩家盘面是否相同
	if _check_boards_identical():
		print("警告: AI和玩家盘面相同! 重新生成AI盘面...")
		if not ai_board.auto_place_ships():  # 尝试重新生成
			print("AI再次生成盘面失败!")
		else:
			print("\n重新生成后的AI盘面:")
			ai_board.print_board_state()
	
	current_state = GameState.PLAYER_TURN
	game_started.emit(&"player")

# 检查两个棋盘是否相同
func _check_boards_identical() -> bool:
	var is_same = player_board.is_same_as(ai_board)
	if is_same:
		print("盘面比较结果: 相同")
		print("玩家盘面:")
		player_board.print_board_state()
		print("AI盘面:")
		ai_board.print_board_state()
	else:
		print("盘面比较结果: 不同")
	return is_same

# 玩家放置船只
func player_place_ship(size: int, x: int, y: int, horizontal: bool) -> bool:
	if current_state != GameState.SETUP:
		return false
	
	var success = player_board.try_place_ship(size, x, y, horizontal)
	
	# 检查是否所有船都放置完毕
	if success and player_board.all_ships_placed:
		start_game()
	
	return success

# 玩家自动放置所有船只
func player_auto_place_ships() -> bool:
	if current_state != GameState.SETUP:
		return false
	
	var success = player_board.auto_place_ships()
	
	if success:
		start_game()
	
	return success

## 处理玩家攻击
##
## @param position: 攻击位置(Vector2i)
## @return: 返回攻击结果字典{hit: bool, sunk: bool, ship_type: StringName}
func player_attack(position: Vector2i) -> Dictionary:
	if current_state != GameState.PLAYER_TURN:
		return {}
	
	if not ai_board.is_valid_attack_position(position):
		return {}
	
	var result: Dictionary = ai_board.take_hit(position)
	attack_result.emit(&"player", position, result)
	
	# 检查是否击沉船只
	if result.sunk:
		ship_sunk.emit(&"ai", result.ship_type)
	
	# 检查游戏是否结束
	if ai_board.all_ships_sunk:
		game_result = &"玩家获胜!"
		current_state = GameState.GAME_OVER
		return result

	# 只有在没有击中时才切换到AI回合
	if not result.hit:
		current_state = GameState.AI_TURN
	# 如果击中了，保持玩家回合，让玩家继续攻击

	return result

## 处理AI回合
##
## @return: 返回攻击结果字典{position: Vector2i, hit: bool, sunk: bool, ship_type: StringName}
func ai_turn() -> Dictionary:
	if current_state != GameState.AI_TURN:
		return {}
	
	# 添加AI思考延迟效果
	await get_tree().create_timer(1.0).timeout
	
	# 根据难度获取攻击位置
	var attack_pos := ai_opponent.get_next_attack()
	var result: Dictionary = player_board.take_hit(attack_pos)
	attack_result.emit(&"ai", attack_pos, result)
	
	# 更新AI决策系统
	ai_opponent.register_attack_result(attack_pos, result)
	
	# 检查是否击沉船只
	if result.sunk:
		ship_sunk.emit(&"player", result.ship_type)
		# 添加额外延迟，突出沉船效果
		await get_tree().create_timer(0.5).timeout
	
	# 检查游戏是否结束
	if player_board.all_ships_sunk:
		game_result = &"AI获胜!"
		current_state = GameState.GAME_OVER
		return result

	# 只有在没有击中时才切换回玩家回合
	if not result.hit:
		current_state = GameState.PLAYER_TURN
	else:
		# 如果击中了，AI继续攻击
		call_deferred("ai_turn")

	return result

# 获取游戏状态文本
func get_state_text() -> String:
	match current_state:
		GameState.SETUP:
			return "放置船只阶段"
		GameState.PLAYER_TURN:
			return "玩家回合"
		GameState.AI_TURN:
			return "AI回合"
		GameState.GAME_OVER:
			return "游戏结束: " + game_result
		_:
			return "未知状态"

# 获取获胜者
func _get_winner() -> StringName:
	if player_board.all_ships_sunk:
		return &"ai"
	elif ai_board.all_ships_sunk:
		return &"player"
	else:
		return &"draw"

# 获取结束原因
func _get_end_reason() -> StringName:
	if player_board.all_ships_sunk or ai_board.all_ships_sunk:
		return &"all_ships_sunk"
	else:
		return &"timeout"

# 检查游戏是否结束
func is_game_over() -> bool:
	return current_state == GameState.GAME_OVER

# 获取玩家棋盘
func get_player_board() -> Board:
	return player_board

# 获取AI棋盘
func get_ai_board() -> Board:
	return ai_board

# 获取当前状态
func get_current_state() -> int:
	return current_state

# 设置AI难度
func set_ai_difficulty(difficulty: int):
	ai_difficulty = difficulty
	if ai_opponent:
		ai_opponent.difficulty = difficulty
